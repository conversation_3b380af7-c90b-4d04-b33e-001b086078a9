import os
import uuid
import pymysql
import time
import datetime

try:
    import redis
except Exception as e:
    print("开始执行安装redis==4.2.2 任务")
    val = os.system('pip install redis==4.2.2 -i https://mirrors.aliyun.com/pypi/simple/')
    import redis  # 二次引入


sep = os.path.sep  # 路径分隔符
cur_dir = os.path.abspath('.') + sep



def gen_headers(app_id, **kwargs):
    header = {
        "Content-Type": "application/json",
        "X-Tsign-Open-App-Id": app_id,
        "X-Tsign-Open-Auth-Mode": "simple"
    }
    headers = {**header, **kwargs}
    return headers

#动态拼接字符串
def concatenate_strings(*args):
    return "".join(args)

#数字加1
def num_add_1(num):
    num = num+1
    return num

#数字相减
def subtract(num1, num2):
    return num1-num2

#生成
def get_uuid():
    return str(uuid.uuid4())



# 查询数据库,返回一个string字符串



#获取当前时间戳秒级
def getTimeStamp_ms():
    t = time.time()
    return int(t*1000)

# 默认数据路径都放在data下
def open_file(local_path):
    fd = open(cur_dir + local_path, 'rb')
    return fd

def hook_sleep_n_secs(n_secs):
    time.sleep(n_secs)

#获取当前日期0点0分0秒的时间戳
def todayStart_getTimeStamp_ms():
    t=datetime.datetime.now()
    #当前日期
    t1 =t.strftime('%Y-%m-%d 00:00:00')
    #转为秒级时间戳
    start_time=time.mktime(time.strptime(t1, '%Y-%m-%d %H:%M:%S'))
    return int(start_time*1000)


#获取当前日期23点59分59秒的时间戳
def todayEnd_getTimeStamp_ms():
    t=datetime.datetime.now()
    #当前日期
    t1 =t.strftime('%Y-%m-%d 23:59:59')
    #转为秒级时间戳
    start_time=time.mktime(time.strptime(t1, '%Y-%m-%d %H:%M:%S'))
    return int(start_time*1000)