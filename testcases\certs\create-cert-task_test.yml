- config:
    name: 创建证书任务测试用例
    base_url: ${ENV(openapi_url)}
    variables:
      - app_Id: ${ENV(app_id)}
      - phone: 19845524061
      - certName: 测试党咏彩
      - licenseNumber: 370200196004223845

- test:
    name: 正向用例_创建证书任务
    variables:
      - json: {
"bizType": "TCLOUD",
"operatorType": "APPLY",
"projectId": "**********",
"notifyUrl": "http://libaohui.com.cn/callback/ding",
"redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai",
"phone": "${phone}",
"applyConfigModel": {
"certNum": 1,
"certType": "SINGLE",
"issuer": "GUOXINCA",
"algorithm": "RSA"
},
"applyCommonModel": {},
"applyUserModel": {
"certName": "${certName}",
"licenseNumber": "${licenseNumber}",
"licenseType": 19,
"userType": 1
}
}
    api: api/certs/create-cert-task.yml
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: 枚举值_licenseType_19
    variables:
      - json: {
"bizType": "TCLOUD",
"operatorType": "APPLY",
"projectId": "**********",
"notifyUrl": "http://libaohui.com.cn/callback/ding",
"redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai",
"phone": "${phone}",
"applyConfigModel": {
"certNum": 1,
"certType": "SINGLE",
"issuer": "GUOXINCA",
"algorithm": "RSA"
},
"applyCommonModel": {},
"applyUserModel": {
"certName": "${certName}",
"licenseNumber": "${licenseNumber}",
"licenseType": 19,
"userType": 1
}
}
    api: api/certs/create-cert-task.yml
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: 枚举值_userType_1
    variables:
      - json: {
"bizType": "TCLOUD",
"operatorType": "APPLY",
"projectId": "**********",
"notifyUrl": "http://libaohui.com.cn/callback/ding",
"redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai",
"phone": "${phone}",
"applyConfigModel": {
"certNum": 1,
"certType": "SINGLE",
"issuer": "GUOXINCA",
"algorithm": "RSA"
},
"applyCommonModel": {},
"applyUserModel": {
"certName": "${certName}",
"licenseNumber": "${licenseNumber}",
"licenseType": 19,
"userType": 1
}
}
    api: api/certs/create-cert-task.yml
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: 非法枚举值_licenseType
    variables:
      - json: {
"bizType": "TCLOUD",
"operatorType": "APPLY",
"projectId": "**********",
"notifyUrl": "http://libaohui.com.cn/callback/ding",
"redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai",
"phone": "${phone}",
"applyConfigModel": {
"certNum": 1,
"certType": "SINGLE",
"issuer": "GUOXINCA",
"algorithm": "RSA"
},
"applyCommonModel": {},
"applyUserModel": {
"certName": "${certName}",
"licenseNumber": "${licenseNumber}",
"licenseType": 999,
"userType": 1
}
}
    api: api/certs/create-cert-task.yml
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 400]
      - contains: ["content.message", "参数错误: licenseType不合法"]

- test:
    name: 缺失必填参数_bizType
    variables:
      - json: {
"operatorType": "APPLY",
"projectId": "**********",
"notifyUrl": "http://libaohui.com.cn/callback/ding",
"redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai",
"phone": "${phone}",
"applyConfigModel": {
"certNum": 1,
"certType": "SINGLE",
"issuer": "GUOXINCA",
"algorithm": "RSA"
},
"applyCommonModel": {},
"applyUserModel": {
"certName": "${certName}",
"licenseNumber": "${licenseNumber}",
"licenseType": 19,
"userType": 1
}
}
    api: api/certs/create-cert-task.yml
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 400]
      - contains: ["content.message", "参数错误: bizType不能为空"]

- test:
    name: 缺失必填参数_operatorType
    variables:
      - json: {
"bizType": "TCLOUD",
"projectId": "**********",
"notifyUrl": "http://libaohui.com.cn/callback/ding",
"redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai",
"phone": "${phone}",
"applyConfigModel": {
"certNum": 1,
"certType": "SINGLE",
"issuer": "GUOXINCA",
"algorithm": "RSA"
},
"applyCommonModel": {},
"applyUserModel": {
"certName": "${certName}",
"licenseNumber": "${licenseNumber}",
"licenseType": 19,
"userType": 1
}
}
    api: api/certs/create-cert-task.yml
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 400]
      - contains: ["content.message", "参数错误: operatorType不能为空"]

- test:
    name: 缺失必填参数_projectId
    variables:
      - json: {
"bizType": "TCLOUD",
"operatorType": "APPLY",
"notifyUrl": "http://libaohui.com.cn/callback/ding",
"redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai",
"phone": "${phone}",
"applyConfigModel": {
"certNum": 1,
"certType": "SINGLE",
"issuer": "GUOXINCA",
"algorithm": "RSA"
},
"applyCommonModel": {},
"applyUserModel": {
"certName": "${certName}",
"licenseNumber": "${licenseNumber}",
"licenseType": 19,
"userType": 1
}
}
    api: api/certs/create-cert-task.yml
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 400]
      - contains: ["content.message", "参数错误: projectId不能为空"]

- test:
    name: 缺失必填参数_notifyUrl
    variables:
      - json: {
"bizType": "TCLOUD",
"operatorType": "APPLY",
"projectId": "**********",
"redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai",
"phone": "${phone}",
"applyConfigModel": {
"certNum": 1,
"certType": "SINGLE",
"issuer": "GUOXINCA",
"algorithm": "RSA"
},
"applyCommonModel": {},
"applyUserModel": {
"certName": "${certName}",
"licenseNumber": "${licenseNumber}",
"licenseType": 19,
"userType": 1
}
}
    api: api/certs/create-cert-task.yml
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 400]
      - contains: ["content.message", "参数错误: notifyUrl不能为空"]

- test:
    name: 缺失必填参数_phone
    variables:
      - json: {
"bizType": "TCLOUD",
"operatorType": "APPLY",
"projectId": "**********",
"notifyUrl": "http://libaohui.com.cn/callback/ding",
"redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai",
"applyConfigModel": {
"certNum": 1,
"certType": "SINGLE",
"issuer": "GUOXINCA",
"algorithm": "RSA"
},
"applyCommonModel": {},
"applyUserModel": {
"certName": "${certName}",
"licenseNumber": "${licenseNumber}",
"licenseType": 19,
"userType": 1
}
}
    api: api/certs/create-cert-task.yml
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 400]
      - contains: ["content.message", "参数错误: phone不能为空"]

- test:
    name: 缺失嵌套必填参数_applyConfigModel.certNum
    variables:
      - json: {
"bizType": "TCLOUD",
"operatorType": "APPLY",
"projectId": "**********",
"notifyUrl": "http://libaohui.com.cn/callback/ding",
"redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai",
"phone": "${phone}",
"applyConfigModel": {
"certType": "SINGLE",
"issuer": "GUOXINCA",
"algorithm": "RSA"
},
"applyCommonModel": {},
"applyUserModel": {
"certName": "${certName}",
"licenseNumber": "${licenseNumber}",
"licenseType": 19,
"userType": 1
}
}
    api: api/certs/create-cert-task.yml
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 400]
      - contains: ["content.message", "参数错误: certNum不能为空"]

- test:
    name: 枚举值_certType_SINGLE
    variables:
      - json: {
"bizType": "TCLOUD",
"operatorType": "APPLY",
"projectId": "**********",
"notifyUrl": "http://libaohui.com.cn/callback/ding",
"redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai",
"phone": "${phone}",
"applyConfigModel": {
"certNum": 1,
"certType": "SINGLE",
"issuer": "GUOXINCA",
"algorithm": "RSA"
},
"applyCommonModel": {},
"applyUserModel": {
"certName": "${certName}",
"licenseNumber": "${licenseNumber}",
"licenseType": 19,
"userType": 1
}
}
    api: api/certs/create-cert-task.yml
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: 非法枚举值_certType
    variables:
      - json: {
"bizType": "TCLOUD",
"operatorType": "APPLY",
"projectId": "**********",
"notifyUrl": "http://libaohui.com.cn/callback/ding",
"redirectUrl": "https://www.baidu.com/?ie=utf-8&tn=25017023_17_dg&wd=bai",
"phone": "${phone}",
"applyConfigModel": {
"certNum": 1,
"certType": "INVALID_TYPE",
"issuer": "GUOXINCA",
"algorithm": "RSA"
},
"applyCommonModel": {},
"applyUserModel": {
"certName": "${certName}",
"licenseNumber": "${licenseNumber}",
"licenseType": 19,
"userType": 1
}
}
    api: api/certs/create-cert-task.yml
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 400]
      - contains: ["content.message", "参数错误: certType不合法"]
