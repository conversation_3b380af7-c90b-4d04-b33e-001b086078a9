request:
  url: "http://cert-service.testk8s.tsign.cn/openca/rest/cert/createnew"
  method: POST
  headers: ${gen_headers($app_Id)}
  json:
    certParam:
      algorithm: "$algorithm"
      certPolicy: "COMMON"
      certTime: "$cert_time"
      certType: "SINGLE"
      configId: "$app_id"
      isUkey: false
      issuer: "ZHCA"
    commonParam:
      address: "天堂软件园"
      mobile: "$phone"
      phone: "$phone"
    userParam:
      certName: "$cert_name"
      licenseNumber: "$idcard"
      licenseType: 19
validate:
  - eq: ["status_code", 200]