<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="JsonStandardCompliance" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="LanguageDetectionInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="27">
            <item index="0" class="java.lang.String" itemvalue="pandas" />
            <item index="1" class="java.lang.String" itemvalue="beautifulsoup4" />
            <item index="2" class="java.lang.String" itemvalue="chinese_calendar" />
            <item index="3" class="java.lang.String" itemvalue="jmespath" />
            <item index="4" class="java.lang.String" itemvalue="selenium" />
            <item index="5" class="java.lang.String" itemvalue="flask_paginate" />
            <item index="6" class="java.lang.String" itemvalue="numpy" />
            <item index="7" class="java.lang.String" itemvalue="PyMySQL" />
            <item index="8" class="java.lang.String" itemvalue="uwsgi" />
            <item index="9" class="java.lang.String" itemvalue="pypinyin" />
            <item index="10" class="java.lang.String" itemvalue="jira" />
            <item index="11" class="java.lang.String" itemvalue="Flask" />
            <item index="12" class="java.lang.String" itemvalue="pymysql" />
            <item index="13" class="java.lang.String" itemvalue="gevent" />
            <item index="14" class="java.lang.String" itemvalue="PySide2" />
            <item index="15" class="java.lang.String" itemvalue="pydantic" />
            <item index="16" class="java.lang.String" itemvalue="rocketmq" />
            <item index="17" class="java.lang.String" itemvalue="PyYAML" />
            <item index="18" class="java.lang.String" itemvalue="python-dateutil" />
            <item index="19" class="java.lang.String" itemvalue="requests" />
            <item index="20" class="java.lang.String" itemvalue="demjson" />
            <item index="21" class="java.lang.String" itemvalue="crypto" />
            <item index="22" class="java.lang.String" itemvalue="eventlet" />
            <item index="23" class="java.lang.String" itemvalue="Faker" />
            <item index="24" class="java.lang.String" itemvalue="jsonpath" />
            <item index="25" class="java.lang.String" itemvalue="httprunner" />
            <item index="26" class="java.lang.String" itemvalue="openpyxl" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>