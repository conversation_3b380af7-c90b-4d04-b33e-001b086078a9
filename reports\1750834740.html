<head>
  <meta content="text/html; charset=utf-8" http-equiv="content-type" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title> - TestReport</title>
  <style>
    body {
      background-color: #f2f2f2;
      color: #333;
      margin: 0 auto;
      width: 960px;
    }
    #summary {
      width: 960px;
      margin-bottom: 20px;
    }
    #summary th {
      background-color: skyblue;
      padding: 5px 12px;
    }
    #summary td {
      background-color: lightblue;
      text-align: center;
      padding: 4px 8px;
    }
    .details {
      width: 960px;
      margin-bottom: 20px;
    }
    .details th {
      background-color: skyblue;
      padding: 5px 12px;
    }
    .details tr .passed {
      background-color: lightgreen;
    }
    .details tr .failed {
      background-color: red;
    }
    .details tr .unchecked {
      background-color: gray;
    }
    .details td {
      background-color: lightblue;
      padding: 5px 12px;
    }
    .details .detail {
      background-color: lightgrey;
      font-size: smaller;
      padding: 5px 10px;
      line-height: 20px;
      text-align: left;
    }
    .details .success {
      background-color: greenyellow;
    }
    .details .error {
      background-color: red;
    }
    .details .failure {
      background-color: salmon;
    }
    .details .skipped {
      background-color: gray;
    }

    .button {
      font-size: 1em;
      padding: 6px;
      width: 4em;
      text-align: center;
      background-color: #06d85f;
      border-radius: 20px/50px;
      cursor: pointer;
      transition: all 0.3s ease-out;
    }
    a.button{
      color: gray;
      text-decoration: none;
      display: inline-block;
    }
    .button:hover {
      background: #2cffbd;
    }

    .overlay {
      position: fixed;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      background: rgba(0, 0, 0, 0.7);
      transition: opacity 500ms;
      visibility: hidden;
      opacity: 0;
      line-height: 25px;
    }
    .overlay:target {
      visibility: visible;
      opacity: 1;
    }

    .popup {
      margin: 70px auto;
      padding: 20px;
      background: #fff;
      border-radius: 10px;
      width: 50%;
      position: relative;
      transition: all 3s ease-in-out;
    }

    .popup h2 {
      margin-top: 0;
      color: #333;
      font-family: Tahoma, Arial, sans-serif;
    }
    .popup .close {
      position: absolute;
      top: 20px;
      right: 30px;
      transition: all 200ms;
      font-size: 30px;
      font-weight: bold;
      text-decoration: none;
      color: #333;
    }
    .popup .close:hover {
      color: #06d85f;
    }
    .popup .content {
      max-height: 80%;
      overflow: auto;
      text-align: left;
    }
    .popup .separator {
      color:royalblue
    }

    @media screen and (max-width: 700px) {
      .box {
        width: 70%;
      }
      .popup {
        width: 70%;
      }
    }

  </style>
</head>

<body>
  <h1>Test Report: </h1>

  <h2>Summary</h2>
  <table id="summary">
    <tr>
      <th>START AT</th>
      <td colspan="4">2025-06-25 14:59:00</td>
    </tr>
    <tr>
      <th>DURATION</th>
      <td colspan="4">3.632 seconds</td>
    </tr>
    <tr>
      <th>PLATFORM</th>
      <td>HttpRunner 2.2.4 </td>
      <td>CPython 3.9.7 </td>
      <td colspan="2">Windows-10-10.0.18363-SP0</td>
    </tr>
    <tr>
      <th>STAT</th>
      <th colspan="2">TESTCASES (success/fail)</th>
      <th colspan="2">TESTSTEPS (success/fail/error/skip)</th>
    </tr>
    <tr>
      <td>total (details) =></td>
      <td colspan="2">1 (0/1)</td>
      <td colspan="2">12 (4/8/0/0)</td>
    </tr>
  </table>

  <h2>Details</h2>

  
  
  <h3>创建证书任务测试用例</h3>
  <table id="suite_1" class="details">
    <tr>
      <td>TOTAL: 12</td>
      <td>SUCCESS: 4</td>
      <td>FAILED: 8</td>
      <td>ERROR: 0</td>
      <td>SKIPPED: 0</td>
    </tr>
    <tr>
      <th>Status</th>
      <th colspan="2">Name</th>
      <th>Response Time</th>
      <th>Detail</th>
    </tr>

    
    
    
    <tr id="record_1_1">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">正向用例_创建证书任务</td>
      <td style="text-align:center;width:6em;">270.86 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_1_1">log-1</a>
        <div id="popup_log_1_1_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_1_1">&times;</a>

            <div class="content">
              <h3>Name: 正向用例_创建证书任务</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722915
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 484
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: &#34;**********&#34;, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19845524061, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1, &#34;certType&#34;: &#34;SINGLE&#34;, &#34;issuer&#34;: &#34;GUOXINCA&#34;, &#34;algorithm&#34;: &#34;RSA&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;\u6d4b\u8bd5\u515a\u548f\u5f69&#34;, &#34;licenseNumber&#34;: 370200196004223845, &#34;licenseType&#34;: 19, &#34;userType&#34;: 1}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 25 Jun 2025 06:59:01 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T289T17508347408346823
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 169
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T289T17508347408346823
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/lCKg7mY&#39;, &#39;taskId&#39;: &#39;79d22795d1774dd588cac31e1000cd91&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=79d22795d1774dd588cac31e1000cd91&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        status_code
                      </td>
                      <td>equals</td>
                      <td>200</td>
                      <td>200</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>equals</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>270.86</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>262.976</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_2">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">枚举值_licenseType_19</td>
      <td style="text-align:center;width:6em;">94.99 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_2_1">log-1</a>
        <div id="popup_log_1_2_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_2_1">&times;</a>

            <div class="content">
              <h3>Name: 枚举值_licenseType_19</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722915
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 484
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: &#34;**********&#34;, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19845524061, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1, &#34;certType&#34;: &#34;SINGLE&#34;, &#34;issuer&#34;: &#34;GUOXINCA&#34;, &#34;algorithm&#34;: &#34;RSA&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;\u6d4b\u8bd5\u515a\u548f\u5f69&#34;, &#34;licenseNumber&#34;: 370200196004223845, &#34;licenseType&#34;: 19, &#34;userType&#34;: 1}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 25 Jun 2025 06:59:01 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T289T17508347410866837
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 30
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T289T17508347410866837
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/szgwsub&#39;, &#39;taskId&#39;: &#39;cfeeeb7b21e94033b64e285345f83a20&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=cfeeeb7b21e94033b64e285345f83a20&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        status_code
                      </td>
                      <td>equals</td>
                      <td>200</td>
                      <td>200</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>equals</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>94.99</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>93.032</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_3">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">枚举值_userType_1</td>
      <td style="text-align:center;width:6em;">105.54 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_3_1">log-1</a>
        <div id="popup_log_1_3_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_3_1">&times;</a>

            <div class="content">
              <h3>Name: 枚举值_userType_1</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722915
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 484
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: &#34;**********&#34;, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19845524061, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1, &#34;certType&#34;: &#34;SINGLE&#34;, &#34;issuer&#34;: &#34;GUOXINCA&#34;, &#34;algorithm&#34;: &#34;RSA&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;\u6d4b\u8bd5\u515a\u548f\u5f69&#34;, &#34;licenseNumber&#34;: 370200196004223845, &#34;licenseType&#34;: 19, &#34;userType&#34;: 1}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 25 Jun 2025 06:59:01 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T289T17508347411936843
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 26
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T289T17508347411936843
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/HqebFIT&#39;, &#39;taskId&#39;: &#39;d1ad07f71ada4b0b8d8fa0b1b3879f23&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=d1ad07f71ada4b0b8d8fa0b1b3879f23&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        status_code
                      </td>
                      <td>equals</td>
                      <td>200</td>
                      <td>200</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>equals</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>105.54</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>103.619</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_4">
      <th class="failure" style="width:5em;">failure</th>
      <td colspan="2">非法枚举值_licenseType</td>
      <td style="text-align:center;width:6em;">77.40 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_4_1">log-1</a>
        <div id="popup_log_1_4_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_4_1">&times;</a>

            <div class="content">
              <h3>Name: 非法枚举值_licenseType</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722915
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 485
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: &#34;**********&#34;, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19845524061, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1, &#34;certType&#34;: &#34;SINGLE&#34;, &#34;issuer&#34;: &#34;GUOXINCA&#34;, &#34;algorithm&#34;: &#34;RSA&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;\u6d4b\u8bd5\u515a\u548f\u5f69&#34;, &#34;licenseNumber&#34;: 370200196004223845, &#34;licenseType&#34;: 999, &#34;userType&#34;: 1}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 25 Jun 2025 06:59:01 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T289T17508347412836847
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 29
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T289T17508347412836847
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 1451001, &#39;message&#39;: None, &#39;data&#39;: None}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        status_code
                      </td>
                      <td>equals</td>
                      <td>200</td>
                      <td>200</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="failed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>400</td>
                      <td>1451001</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="failed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>参数错误: licenseType不合法</td>
                      <td>None</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>43</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>77.4</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>75.784</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        
          <a class="button" href="#popup_attachment_1_4">traceback</a>
          <div id="popup_attachment_1_4" class="overlay">
            <div class="popup">
              <h2>Traceback Message</h2>
              <a class="close" href="#record_1_4">&times;</a>
              <div class="content"><pre>Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 54, in test
    test_runner.run_test(test_dict)
httprunner.exceptions.ValidationFailure: 
validate: content.code equals 400(int)	==&gt; fail
1451001(int) equals 400(int)

validate: content.message contains 参数错误: licenseType不合法(str)	==&gt; fail
None(NoneType) contains 参数错误: licenseType不合法(str)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 56, in test
    self.fail(str(ex))
AssertionError: 
validate: content.code equals 400(int)	==&gt; fail
1451001(int) equals 400(int)

validate: content.message contains 参数错误: licenseType不合法(str)	==&gt; fail
None(NoneType) contains 参数错误: licenseType不合法(str)
</pre></div>
            </div>
          </div>
        

      </td>
    </tr>
  
    
    
    <tr id="record_1_5">
      <th class="failure" style="width:5em;">failure</th>
      <td colspan="2">缺失必填参数_bizType</td>
      <td style="text-align:center;width:6em;">94.34 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_5_1">log-1</a>
        <div id="popup_log_1_5_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_5_1">&times;</a>

            <div class="content">
              <h3>Name: 缺失必填参数_bizType</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722915
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 463
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: &#34;**********&#34;, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19845524061, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1, &#34;certType&#34;: &#34;SINGLE&#34;, &#34;issuer&#34;: &#34;GUOXINCA&#34;, &#34;algorithm&#34;: &#34;RSA&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;\u6d4b\u8bd5\u515a\u548f\u5f69&#34;, &#34;licenseNumber&#34;: 370200196004223845, &#34;licenseType&#34;: 19, &#34;userType&#34;: 1}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 25 Jun 2025 06:59:01 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T289T17508347413486851
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 12
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T289T17508347413486851
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 1451000, &#39;message&#39;: &#39;参数错误: bizType不能为空&#39;, &#39;data&#39;: None}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        status_code
                      </td>
                      <td>equals</td>
                      <td>200</td>
                      <td>200</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="failed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>400</td>
                      <td>1451000</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>参数错误: bizType不能为空</td>
                      <td>参数错误: bizType不能为空</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>74</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>94.34</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>92.542</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        
          <a class="button" href="#popup_attachment_1_5">traceback</a>
          <div id="popup_attachment_1_5" class="overlay">
            <div class="popup">
              <h2>Traceback Message</h2>
              <a class="close" href="#record_1_5">&times;</a>
              <div class="content"><pre>Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 54, in test
    test_runner.run_test(test_dict)
httprunner.exceptions.ValidationFailure: 
validate: content.code equals 400(int)	==&gt; fail
1451000(int) equals 400(int)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 56, in test
    self.fail(str(ex))
AssertionError: 
validate: content.code equals 400(int)	==&gt; fail
1451000(int) equals 400(int)
</pre></div>
            </div>
          </div>
        

      </td>
    </tr>
  
    
    
    <tr id="record_1_6">
      <th class="failure" style="width:5em;">failure</th>
      <td colspan="2">缺失必填参数_operatorType</td>
      <td style="text-align:center;width:6em;">48.88 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_6_1">log-1</a>
        <div id="popup_log_1_6_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_6_1">&times;</a>

            <div class="content">
              <h3>Name: 缺失必填参数_operatorType</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722915
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 459
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;projectId&#34;: &#34;**********&#34;, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19845524061, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1, &#34;certType&#34;: &#34;SINGLE&#34;, &#34;issuer&#34;: &#34;GUOXINCA&#34;, &#34;algorithm&#34;: &#34;RSA&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;\u6d4b\u8bd5\u515a\u548f\u5f69&#34;, &#34;licenseNumber&#34;: 370200196004223845, &#34;licenseType&#34;: 19, &#34;userType&#34;: 1}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 25 Jun 2025 06:59:01 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T289T17508347414536855
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 10
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T289T17508347414536855
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 1451000, &#39;message&#39;: &#39;参数错误: operatorType不能为空&#39;, &#39;data&#39;: None}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        status_code
                      </td>
                      <td>equals</td>
                      <td>200</td>
                      <td>200</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="failed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>400</td>
                      <td>1451000</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>参数错误: operatorType不能为空</td>
                      <td>参数错误: operatorType不能为空</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>79</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>48.88</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>47.127</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        
          <a class="button" href="#popup_attachment_1_6">traceback</a>
          <div id="popup_attachment_1_6" class="overlay">
            <div class="popup">
              <h2>Traceback Message</h2>
              <a class="close" href="#record_1_6">&times;</a>
              <div class="content"><pre>Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 54, in test
    test_runner.run_test(test_dict)
httprunner.exceptions.ValidationFailure: 
validate: content.code equals 400(int)	==&gt; fail
1451000(int) equals 400(int)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 56, in test
    self.fail(str(ex))
AssertionError: 
validate: content.code equals 400(int)	==&gt; fail
1451000(int) equals 400(int)
</pre></div>
            </div>
          </div>
        

      </td>
    </tr>
  
    
    
    <tr id="record_1_7">
      <th class="failure" style="width:5em;">failure</th>
      <td colspan="2">缺失必填参数_projectId</td>
      <td style="text-align:center;width:6em;">755.03 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_7_1">log-1</a>
        <div id="popup_log_1_7_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_7_1">&times;</a>

            <div class="content">
              <h3>Name: 缺失必填参数_projectId</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722915
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 457
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19845524061, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1, &#34;certType&#34;: &#34;SINGLE&#34;, &#34;issuer&#34;: &#34;GUOXINCA&#34;, &#34;algorithm&#34;: &#34;RSA&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;\u6d4b\u8bd5\u515a\u548f\u5f69&#34;, &#34;licenseNumber&#34;: 370200196004223845, &#34;licenseType&#34;: 19, &#34;userType&#34;: 1}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 25 Jun 2025 06:59:02 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T289T17508347415106857
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 702
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T289T17508347415106857
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/VkDwz1b&#39;, &#39;taskId&#39;: &#39;19154651189848cd8311c1c5b4e1d0c3&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=19154651189848cd8311c1c5b4e1d0c3&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        status_code
                      </td>
                      <td>equals</td>
                      <td>200</td>
                      <td>200</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="failed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>400</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="failed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>参数错误: projectId不能为空</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>755.03</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>753.67</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        
          <a class="button" href="#popup_attachment_1_7">traceback</a>
          <div id="popup_attachment_1_7" class="overlay">
            <div class="popup">
              <h2>Traceback Message</h2>
              <a class="close" href="#record_1_7">&times;</a>
              <div class="content"><pre>Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 54, in test
    test_runner.run_test(test_dict)
httprunner.exceptions.ValidationFailure: 
validate: content.code equals 400(int)	==&gt; fail
0(int) equals 400(int)

validate: content.message contains 参数错误: projectId不能为空(str)	==&gt; fail
成功(str) contains 参数错误: projectId不能为空(str)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 56, in test
    self.fail(str(ex))
AssertionError: 
validate: content.code equals 400(int)	==&gt; fail
0(int) equals 400(int)

validate: content.message contains 参数错误: projectId不能为空(str)	==&gt; fail
成功(str) contains 参数错误: projectId不能为空(str)
</pre></div>
            </div>
          </div>
        

      </td>
    </tr>
  
    
    
    <tr id="record_1_8">
      <th class="failure" style="width:5em;">failure</th>
      <td colspan="2">缺失必填参数_notifyUrl</td>
      <td style="text-align:center;width:6em;">149.86 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_8_1">log-1</a>
        <div id="popup_log_1_8_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_8_1">&times;</a>

            <div class="content">
              <h3>Name: 缺失必填参数_notifyUrl</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722915
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 431
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: &#34;**********&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19845524061, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1, &#34;certType&#34;: &#34;SINGLE&#34;, &#34;issuer&#34;: &#34;GUOXINCA&#34;, &#34;algorithm&#34;: &#34;RSA&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;\u6d4b\u8bd5\u515a\u548f\u5f69&#34;, &#34;licenseNumber&#34;: 370200196004223845, &#34;licenseType&#34;: 19, &#34;userType&#34;: 1}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 25 Jun 2025 06:59:02 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T289T17508347422616879
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 102
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T289T17508347422616879
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/RtH55Vu&#39;, &#39;taskId&#39;: &#39;46a16f708836420c8083222967d169f5&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=46a16f708836420c8083222967d169f5&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        status_code
                      </td>
                      <td>equals</td>
                      <td>200</td>
                      <td>200</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="failed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>400</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="failed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>参数错误: notifyUrl不能为空</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>149.86</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>147.457</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        
          <a class="button" href="#popup_attachment_1_8">traceback</a>
          <div id="popup_attachment_1_8" class="overlay">
            <div class="popup">
              <h2>Traceback Message</h2>
              <a class="close" href="#record_1_8">&times;</a>
              <div class="content"><pre>Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 54, in test
    test_runner.run_test(test_dict)
httprunner.exceptions.ValidationFailure: 
validate: content.code equals 400(int)	==&gt; fail
0(int) equals 400(int)

validate: content.message contains 参数错误: notifyUrl不能为空(str)	==&gt; fail
成功(str) contains 参数错误: notifyUrl不能为空(str)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 56, in test
    self.fail(str(ex))
AssertionError: 
validate: content.code equals 400(int)	==&gt; fail
0(int) equals 400(int)

validate: content.message contains 参数错误: notifyUrl不能为空(str)	==&gt; fail
成功(str) contains 参数错误: notifyUrl不能为空(str)
</pre></div>
            </div>
          </div>
        

      </td>
    </tr>
  
    
    
    <tr id="record_1_9">
      <th class="failure" style="width:5em;">failure</th>
      <td colspan="2">缺失必填参数_phone</td>
      <td style="text-align:center;width:6em;">382.64 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_9_1">log-1</a>
        <div id="popup_log_1_9_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_9_1">&times;</a>

            <div class="content">
              <h3>Name: 缺失必填参数_phone</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722915
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 462
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: &#34;**********&#34;, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1, &#34;certType&#34;: &#34;SINGLE&#34;, &#34;issuer&#34;: &#34;GUOXINCA&#34;, &#34;algorithm&#34;: &#34;RSA&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;\u6d4b\u8bd5\u515a\u548f\u5f69&#34;, &#34;licenseNumber&#34;: 370200196004223845, &#34;licenseType&#34;: 19, &#34;userType&#34;: 1}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 25 Jun 2025 06:59:02 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T289T17508347424166889
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 345
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T289T17508347424166889
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/9E3BEye&#39;, &#39;taskId&#39;: &#39;ae65fa6aabdd458f9a735ae2831f2772&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=ae65fa6aabdd458f9a735ae2831f2772&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        status_code
                      </td>
                      <td>equals</td>
                      <td>200</td>
                      <td>200</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="failed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>400</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="failed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>参数错误: phone不能为空</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>382.64</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>380.882</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        
          <a class="button" href="#popup_attachment_1_9">traceback</a>
          <div id="popup_attachment_1_9" class="overlay">
            <div class="popup">
              <h2>Traceback Message</h2>
              <a class="close" href="#record_1_9">&times;</a>
              <div class="content"><pre>Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 54, in test
    test_runner.run_test(test_dict)
httprunner.exceptions.ValidationFailure: 
validate: content.code equals 400(int)	==&gt; fail
0(int) equals 400(int)

validate: content.message contains 参数错误: phone不能为空(str)	==&gt; fail
成功(str) contains 参数错误: phone不能为空(str)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 56, in test
    self.fail(str(ex))
AssertionError: 
validate: content.code equals 400(int)	==&gt; fail
0(int) equals 400(int)

validate: content.message contains 参数错误: phone不能为空(str)	==&gt; fail
成功(str) contains 参数错误: phone不能为空(str)
</pre></div>
            </div>
          </div>
        

      </td>
    </tr>
  
    
    
    <tr id="record_1_10">
      <th class="failure" style="width:5em;">failure</th>
      <td colspan="2">缺失嵌套必填参数_applyConfigModel.certNum</td>
      <td style="text-align:center;width:6em;">196.20 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_10_1">log-1</a>
        <div id="popup_log_1_10_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_10_1">&times;</a>

            <div class="content">
              <h3>Name: 缺失嵌套必填参数_applyConfigModel.certNum</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722915
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 470
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: &#34;**********&#34;, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19845524061, &#34;applyConfigModel&#34;: {&#34;certType&#34;: &#34;SINGLE&#34;, &#34;issuer&#34;: &#34;GUOXINCA&#34;, &#34;algorithm&#34;: &#34;RSA&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;\u6d4b\u8bd5\u515a\u548f\u5f69&#34;, &#34;licenseNumber&#34;: 370200196004223845, &#34;licenseType&#34;: 19, &#34;userType&#34;: 1}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 25 Jun 2025 06:59:02 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T289T17508347428136893
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 148
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T289T17508347428136893
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/CSk5HAN&#39;, &#39;taskId&#39;: &#39;e98ce73ae76047288d26b5e1352ac548&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=e98ce73ae76047288d26b5e1352ac548&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        status_code
                      </td>
                      <td>equals</td>
                      <td>200</td>
                      <td>200</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="failed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>400</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="failed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>参数错误: certNum不能为空</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>196.2</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>194.789</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        
          <a class="button" href="#popup_attachment_1_10">traceback</a>
          <div id="popup_attachment_1_10" class="overlay">
            <div class="popup">
              <h2>Traceback Message</h2>
              <a class="close" href="#record_1_10">&times;</a>
              <div class="content"><pre>Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 54, in test
    test_runner.run_test(test_dict)
httprunner.exceptions.ValidationFailure: 
validate: content.code equals 400(int)	==&gt; fail
0(int) equals 400(int)

validate: content.message contains 参数错误: certNum不能为空(str)	==&gt; fail
成功(str) contains 参数错误: certNum不能为空(str)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 56, in test
    self.fail(str(ex))
AssertionError: 
validate: content.code equals 400(int)	==&gt; fail
0(int) equals 400(int)

validate: content.message contains 参数错误: certNum不能为空(str)	==&gt; fail
成功(str) contains 参数错误: certNum不能为空(str)
</pre></div>
            </div>
          </div>
        

      </td>
    </tr>
  
    
    
    <tr id="record_1_11">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">枚举值_certType_SINGLE</td>
      <td style="text-align:center;width:6em;">971.70 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_11_1">log-1</a>
        <div id="popup_log_1_11_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_11_1">&times;</a>

            <div class="content">
              <h3>Name: 枚举值_certType_SINGLE</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722915
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 484
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: &#34;**********&#34;, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19845524061, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1, &#34;certType&#34;: &#34;SINGLE&#34;, &#34;issuer&#34;: &#34;GUOXINCA&#34;, &#34;algorithm&#34;: &#34;RSA&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;\u6d4b\u8bd5\u515a\u548f\u5f69&#34;, &#34;licenseNumber&#34;: 370200196004223845, &#34;licenseType&#34;: 19, &#34;userType&#34;: 1}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 25 Jun 2025 06:59:03 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T289T17508347430066903
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 904
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T289T17508347430066903
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/Ktn66pr&#39;, &#39;taskId&#39;: &#39;695f1ce0171c46a3be8052e5e346222b&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=695f1ce0171c46a3be8052e5e346222b&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        status_code
                      </td>
                      <td>equals</td>
                      <td>200</td>
                      <td>200</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>equals</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>971.7</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>969.529</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_12">
      <th class="failure" style="width:5em;">failure</th>
      <td colspan="2">非法枚举值_certType</td>
      <td style="text-align:center;width:6em;">420.59 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_12_1">log-1</a>
        <div id="popup_log_1_12_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_12_1">&times;</a>

            <div class="content">
              <h3>Name: 非法枚举值_certType</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722915
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 490
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: &#34;**********&#34;, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19845524061, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1, &#34;certType&#34;: &#34;INVALID_TYPE&#34;, &#34;issuer&#34;: &#34;GUOXINCA&#34;, &#34;algorithm&#34;: &#34;RSA&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;\u6d4b\u8bd5\u515a\u548f\u5f69&#34;, &#34;licenseNumber&#34;: 370200196004223845, &#34;licenseType&#34;: 19, &#34;userType&#34;: 1}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 25 Jun 2025 06:59:04 GMT
                            </div>
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Ts-Request-Id</strong>: 10100016160T289T17508347439756929
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 385
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T289T17508347439756929
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/bvrZqGS&#39;, &#39;taskId&#39;: &#39;cdf7c33563f643ddaf6a18024adc0b5e&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=cdf7c33563f643ddaf6a18024adc0b5e&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        status_code
                      </td>
                      <td>equals</td>
                      <td>200</td>
                      <td>200</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="failed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>400</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="failed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>参数错误: certType不合法</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>420.59</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>418.756</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        
          <a class="button" href="#popup_attachment_1_12">traceback</a>
          <div id="popup_attachment_1_12" class="overlay">
            <div class="popup">
              <h2>Traceback Message</h2>
              <a class="close" href="#record_1_12">&times;</a>
              <div class="content"><pre>Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 54, in test
    test_runner.run_test(test_dict)
httprunner.exceptions.ValidationFailure: 
validate: content.code equals 400(int)	==&gt; fail
0(int) equals 400(int)

validate: content.message contains 参数错误: certType不合法(str)	==&gt; fail
成功(str) contains 参数错误: certType不合法(str)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 56, in test
    self.fail(str(ex))
AssertionError: 
validate: content.code equals 400(int)	==&gt; fail
0(int) equals 400(int)

validate: content.message contains 参数错误: certType不合法(str)	==&gt; fail
成功(str) contains 参数错误: certType不合法(str)
</pre></div>
            </div>
          </div>
        

      </td>
    </tr>
  
  </table>
  
</body>