- config:
    name: "证书全流程测试"
    base_url: "http://cert-service.testk8s.tsign.cn"
    variables:
      - app_id: "**********"
      - app_Id: "**********"
      - algorithm: "SM2"
      - cert_time: "ONEYEAR"

- test:
    name: "步骤1-获取测试账号"
    api: api/cert/test_account.yml
    extract:
      - phone: content.accountList.0.phone
      - idcard: content.accountList.0.idNo
      - name: content.accountList.0.name
    validate:
      - eq: ["status_code", 200]

- test:
    name: "步骤2-创建证书"
    api: api/cert/create_certificate.yml
    variables:
      - phone: $phone
      - idcard: $idcard
      - cert_name: $name
    extract:
      - cert_info_id: content.certInfoId
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.msg", "成功"]

- test:
    name: "步骤3-查询证书信息"
    api: api/cert/query_certificate_detail.yml
    variables:
      - cert_info_id: $cert_info_id
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.certInfo.certname", "$name"]

- test:
    name: "步骤4-吊销证书"
    api: api/cert/revoke_certificate.yml
    variables:
      - cert_info_id: $cert_info_id
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.msg", "成功"]

- test:
    name: "步骤5-验证证书已吊销"
    api: api/cert/query_certificate_detail.yml
    variables:
      - cert_info_id: $cert_info_id
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.msg", "证书记录不存在"]