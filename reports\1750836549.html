<head>
  <meta content="text/html; charset=utf-8" http-equiv="content-type" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title> - TestReport</title>
  <style>
    body {
      background-color: #f2f2f2;
      color: #333;
      margin: 0 auto;
      width: 960px;
    }
    #summary {
      width: 960px;
      margin-bottom: 20px;
    }
    #summary th {
      background-color: skyblue;
      padding: 5px 12px;
    }
    #summary td {
      background-color: lightblue;
      text-align: center;
      padding: 4px 8px;
    }
    .details {
      width: 960px;
      margin-bottom: 20px;
    }
    .details th {
      background-color: skyblue;
      padding: 5px 12px;
    }
    .details tr .passed {
      background-color: lightgreen;
    }
    .details tr .failed {
      background-color: red;
    }
    .details tr .unchecked {
      background-color: gray;
    }
    .details td {
      background-color: lightblue;
      padding: 5px 12px;
    }
    .details .detail {
      background-color: lightgrey;
      font-size: smaller;
      padding: 5px 10px;
      line-height: 20px;
      text-align: left;
    }
    .details .success {
      background-color: greenyellow;
    }
    .details .error {
      background-color: red;
    }
    .details .failure {
      background-color: salmon;
    }
    .details .skipped {
      background-color: gray;
    }

    .button {
      font-size: 1em;
      padding: 6px;
      width: 4em;
      text-align: center;
      background-color: #06d85f;
      border-radius: 20px/50px;
      cursor: pointer;
      transition: all 0.3s ease-out;
    }
    a.button{
      color: gray;
      text-decoration: none;
      display: inline-block;
    }
    .button:hover {
      background: #2cffbd;
    }

    .overlay {
      position: fixed;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      background: rgba(0, 0, 0, 0.7);
      transition: opacity 500ms;
      visibility: hidden;
      opacity: 0;
      line-height: 25px;
    }
    .overlay:target {
      visibility: visible;
      opacity: 1;
    }

    .popup {
      margin: 70px auto;
      padding: 20px;
      background: #fff;
      border-radius: 10px;
      width: 50%;
      position: relative;
      transition: all 3s ease-in-out;
    }

    .popup h2 {
      margin-top: 0;
      color: #333;
      font-family: Tahoma, Arial, sans-serif;
    }
    .popup .close {
      position: absolute;
      top: 20px;
      right: 30px;
      transition: all 200ms;
      font-size: 30px;
      font-weight: bold;
      text-decoration: none;
      color: #333;
    }
    .popup .close:hover {
      color: #06d85f;
    }
    .popup .content {
      max-height: 80%;
      overflow: auto;
      text-align: left;
    }
    .popup .separator {
      color:royalblue
    }

    @media screen and (max-width: 700px) {
      .box {
        width: 70%;
      }
      .popup {
        width: 70%;
      }
    }

  </style>
</head>

<body>
  <h1>Test Report: </h1>

  <h2>Summary</h2>
  <table id="summary">
    <tr>
      <th>START AT</th>
      <td colspan="4">2025-06-25 15:29:09</td>
    </tr>
    <tr>
      <th>DURATION</th>
      <td colspan="4">0.297 seconds</td>
    </tr>
    <tr>
      <th>PLATFORM</th>
      <td>HttpRunner 2.2.4 </td>
      <td>CPython 3.9.7 </td>
      <td colspan="2">Windows-10-10.0.18363-SP0</td>
    </tr>
    <tr>
      <th>STAT</th>
      <th colspan="2">TESTCASES (success/fail)</th>
      <th colspan="2">TESTSTEPS (success/fail/error/skip)</th>
    </tr>
    <tr>
      <td>total (details) =></td>
      <td colspan="2">1 (0/1)</td>
      <td colspan="2">5 (0/2/3/0)</td>
    </tr>
  </table>

  <h2>Details</h2>

  
  
  <h3>证书全流程测试</h3>
  <table id="suite_1" class="details">
    <tr>
      <td>TOTAL: 5</td>
      <td>SUCCESS: 0</td>
      <td>FAILED: 2</td>
      <td>ERROR: 3</td>
      <td>SKIPPED: 0</td>
    </tr>
    <tr>
      <th>Status</th>
      <th colspan="2">Name</th>
      <th>Response Time</th>
      <th>Detail</th>
    </tr>

    
    
    
    <tr id="record_1_1">
      <th class="failure" style="width:5em;">failure</th>
      <td colspan="2">步骤1-获取测试账号</td>
      <td style="text-align:center;width:6em;">97.44 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_1_1">log-1</a>
        <div id="popup_log_1_1_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_1_1">&times;</a>

            <div class="content">
              <h3>Name: 步骤1-获取测试账号</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://sdk.testk8s.tsign.cn/random/get
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          GET
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://sdk.testk8s.tsign.cn/random/get
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            utf-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 25 Jun 2025 07:29:09 GMT
                            </div>
                            
                            <div>
                              <strong>Vary</strong>: Accept-Encoding, Origin, Access-Control-Request-Method, Access-Control-Request-Headers
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;success&#39;: True, &#39;accountList&#39;: [{&#39;orgCode&#39;: &#39;910000002A6CMFLAXP&#39;, &#39;idNo&#39;: &#39;542127198206071867&#39;, &#39;name&#39;: &#39;测试木静荷&#39;, &#39;englishName&#39;: &#39;Lynn Karen Smith&#39;, &#39;bankCard&#39;: &#39;****************&#39;, &#39;phone&#39;: &#39;***********&#39;, &#39;orgName&#39;: &#39;esigntest木静荷经营的个体工商户&#39;}]}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        status_code
                      </td>
                      <td>equals</td>
                      <td>200</td>
                      <td>200</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>258</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>97.44</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>93.064</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        
          <a class="button" href="#popup_attachment_1_1">traceback</a>
          <div id="popup_attachment_1_1" class="overlay">
            <div class="popup">
              <h2>Traceback Message</h2>
              <a class="close" href="#record_1_1">&times;</a>
              <div class="content"><pre>Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 54, in test
    test_runner.run_test(test_dict)
httprunner.exceptions.ExtractFailure: Failed to extract! =&gt; status
response body: {&#39;success&#39;: True, &#39;accountList&#39;: [{&#39;orgCode&#39;: &#39;910000002A6CMFLAXP&#39;, &#39;idNo&#39;: &#39;542127198206071867&#39;, &#39;name&#39;: &#39;测试木静荷&#39;, &#39;englishName&#39;: &#39;Lynn Karen Smith&#39;, &#39;bankCard&#39;: &#39;****************&#39;, &#39;phone&#39;: &#39;***********&#39;, &#39;orgName&#39;: &#39;esigntest木静荷经营的个体工商户&#39;}]}


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 56, in test
    self.fail(str(ex))
AssertionError: Failed to extract! =&gt; status
response body: {&#39;success&#39;: True, &#39;accountList&#39;: [{&#39;orgCode&#39;: &#39;910000002A6CMFLAXP&#39;, &#39;idNo&#39;: &#39;542127198206071867&#39;, &#39;name&#39;: &#39;测试木静荷&#39;, &#39;englishName&#39;: &#39;Lynn Karen Smith&#39;, &#39;bankCard&#39;: &#39;****************&#39;, &#39;phone&#39;: &#39;***********&#39;, &#39;orgName&#39;: &#39;esigntest木静荷经营的个体工商户&#39;}]}

</pre></div>
            </div>
          </div>
        

      </td>
    </tr>
  
    
    
    <tr id="record_1_2">
      <th class="failure" style="width:5em;">failure</th>
      <td colspan="2">步骤2-创建证书</td>
      <td style="text-align:center;width:6em;">168.49 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_2_1">log-1</a>
        <div id="popup_log_1_2_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_2_1">&times;</a>

            <div class="content">
              <h3>Name: 步骤2-创建证书</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://cert-service.testk8s.tsign.cn/openca/rest/cert/createnew
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: **********
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 389
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;certParam&#34;: {&#34;algorithm&#34;: &#34;SM2&#34;, &#34;certPolicy&#34;: &#34;COMMON&#34;, &#34;certTime&#34;: &#34;ONEYEAR&#34;, &#34;certType&#34;: &#34;SINGLE&#34;, &#34;configId&#34;: &#34;**********&#34;, &#34;isUkey&#34;: false, &#34;issuer&#34;: &#34;ZHCA&#34;}, &#34;commonParam&#34;: {&#34;address&#34;: &#34;\u5929\u5802\u8f6f\u4ef6\u56ed&#34;, &#34;mobile&#34;: &#34;***********&#34;, &#34;phone&#34;: &#34;***********&#34;}, &#34;userParam&#34;: {&#34;certName&#34;: &#34;\u6d4b\u8bd5\u8bc1\u4e66&#34;, &#34;licenseNumber&#34;: &#34;542127198206071867&#34;, &#34;licenseType&#34;: 19}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://cert-service.testk8s.tsign.cn/openca/rest/cert/createnew
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 25 Jun 2025 07:29:09 GMT
                            </div>
                            
                            <div>
                              <strong>Vary</strong>: Accept-Encoding
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;errCode&#39;: 0, &#39;msg&#39;: &#39;成功&#39;, &#39;errShow&#39;: False, &#39;signCert&#39;: &#39;MIIC+zCCAqCgAwIBAgIMOJKUU85U1TEJIi1HMAwGCCqBHM9VAYN1BQAwKDEZMBcGA1UEAwwQVEVTVCBaSENBIFNNMiBDQTELMAkGA1UEBhMCQ04wHhcNMjUwNjI1MDcyNjU3WhcNMjYwNjI1MDcyNjU3WjAkMRUwEwYDVQQDDAzmtYvor5Xor4HkuaYxCzAJBgNVBAYTAkNOMFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEJ779D8o6CuWvilt2JKRVbtgMffk5hRhMpndKY73nFcP2P7OUuvEKk0bPrxN4BKqcmNB7EAOFtmSLtfEyzZzRt6OCAbAwggGsMB8GA1UdIwQYMBaAFJR5Q22ZaA/FWsypkn58HUCZKRdaMFcGA1UdIARQME4wTAYEVR0gADBEMEIGCCsGAQUFBwIBFjZodHRwczovL3d3dy50anpoY2EuY29tL0hvbWUvU3VwcG9ydFNlcnZpY2VzP3N0YXRlPXRhYjcwgZgGCCsGAQUFBwEBBIGLMIGIMEIGCCsGAQUFBzABhjZodHRwczovL3d3dy50anpoY2EuY29tL0hvbWUvU3VwcG9ydFNlcnZpY2VzP3N0YXRlPXRhYjMwQgYIKwYBBQUHMAKGNmh0dHBzOi8vd3d3LnRqemhjYS5jb20vSG9tZS9TdXBwb3J0U2VydmljZXM/c3RhdGU9dGFiMzA3BgNVHR8EMDAuMCygKqAohiZodHRwOi8veGtleS50anpoY2EuY29tOjEwMDIxLzkvMTc2LmNybDAJBgNVHRMEAjAAMCUGCCqBHNAUBAEBAQH/BBagFBMSNTQyMTI3MTk4MjA2MDcxODY3MB0GA1UdDgQWBBRMFWbm7HVnKp1oNEQ9ZJ/YZCk73jALBgNVHQ8EBAMCBsAwDAYIKoEcz1UBg3UFAANHADBEAiA28viGyig8s2LqkeggNBPdQyPTcd2N/8FoVJd2ThllbwIgb8zouCZHRAEA2TVKLZ1SlMbgj9YwsGHq92oVrxvJDao=&#39;, &#39;encCert&#39;: None, &#39;privateKey&#39;: &#39;AMhHLPnc6941Y6pEIhgmLX0jGSF3C8U94pRczqlR9sC+\n&#39;, &#39;extention&#39;: &#39;{&#34;encKey&#34;:&#34;&#34;,&#34;timeUsed&#34;:7,&#34;transactionCode&#34;:&#34;2506251526575849332&#34;}&#39;, &#39;certInfoId&#39;: -189818, &#39;algorithm&#39;: &#39;SM2&#39;}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>1289</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>168.49</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>166.826</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        
          <a class="button" href="#popup_attachment_1_2">traceback</a>
          <div id="popup_attachment_1_2" class="overlay">
            <div class="popup">
              <h2>Traceback Message</h2>
              <a class="close" href="#record_1_2">&times;</a>
              <div class="content"><pre>Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 54, in test
    test_runner.run_test(test_dict)
httprunner.exceptions.ExtractFailure: Failed to extract! =&gt; data.certInfoId
response body: {&#39;errCode&#39;: 0, &#39;msg&#39;: &#39;成功&#39;, &#39;errShow&#39;: False, &#39;signCert&#39;: &#39;MIIC+zCCAqCgAwIBAgIMOJKUU85U1TEJIi1HMAwGCCqBHM9VAYN1BQAwKDEZMBcGA1UEAwwQVEVTVCBaSENBIFNNMiBDQTELMAkGA1UEBhMCQ04wHhcNMjUwNjI1MDcyNjU3WhcNMjYwNjI1MDcyNjU3WjAkMRUwEwYDVQQDDAzmtYvor5Xor4HkuaYxCzAJBgNVBAYTAkNOMFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEJ779D8o6CuWvilt2JKRVbtgMffk5hRhMpndKY73nFcP2P7OUuvEKk0bPrxN4BKqcmNB7EAOFtmSLtfEyzZzRt6OCAbAwggGsMB8GA1UdIwQYMBaAFJR5Q22ZaA/FWsypkn58HUCZKRdaMFcGA1UdIARQME4wTAYEVR0gADBEMEIGCCsGAQUFBwIBFjZodHRwczovL3d3dy50anpoY2EuY29tL0hvbWUvU3VwcG9ydFNlcnZpY2VzP3N0YXRlPXRhYjcwgZgGCCsGAQUFBwEBBIGLMIGIMEIGCCsGAQUFBzABhjZodHRwczovL3d3dy50anpoY2EuY29tL0hvbWUvU3VwcG9ydFNlcnZpY2VzP3N0YXRlPXRhYjMwQgYIKwYBBQUHMAKGNmh0dHBzOi8vd3d3LnRqemhjYS5jb20vSG9tZS9TdXBwb3J0U2VydmljZXM/c3RhdGU9dGFiMzA3BgNVHR8EMDAuMCygKqAohiZodHRwOi8veGtleS50anpoY2EuY29tOjEwMDIxLzkvMTc2LmNybDAJBgNVHRMEAjAAMCUGCCqBHNAUBAEBAQH/BBagFBMSNTQyMTI3MTk4MjA2MDcxODY3MB0GA1UdDgQWBBRMFWbm7HVnKp1oNEQ9ZJ/YZCk73jALBgNVHQ8EBAMCBsAwDAYIKoEcz1UBg3UFAANHADBEAiA28viGyig8s2LqkeggNBPdQyPTcd2N/8FoVJd2ThllbwIgb8zouCZHRAEA2TVKLZ1SlMbgj9YwsGHq92oVrxvJDao=&#39;, &#39;encCert&#39;: None, &#39;privateKey&#39;: &#39;AMhHLPnc6941Y6pEIhgmLX0jGSF3C8U94pRczqlR9sC+\n&#39;, &#39;extention&#39;: &#39;{&#34;encKey&#34;:&#34;&#34;,&#34;timeUsed&#34;:7,&#34;transactionCode&#34;:&#34;2506251526575849332&#34;}&#39;, &#39;certInfoId&#39;: -189818, &#39;algorithm&#39;: &#39;SM2&#39;}


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 56, in test
    self.fail(str(ex))
AssertionError: Failed to extract! =&gt; data.certInfoId
response body: {&#39;errCode&#39;: 0, &#39;msg&#39;: &#39;成功&#39;, &#39;errShow&#39;: False, &#39;signCert&#39;: &#39;MIIC+zCCAqCgAwIBAgIMOJKUU85U1TEJIi1HMAwGCCqBHM9VAYN1BQAwKDEZMBcGA1UEAwwQVEVTVCBaSENBIFNNMiBDQTELMAkGA1UEBhMCQ04wHhcNMjUwNjI1MDcyNjU3WhcNMjYwNjI1MDcyNjU3WjAkMRUwEwYDVQQDDAzmtYvor5Xor4HkuaYxCzAJBgNVBAYTAkNOMFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEJ779D8o6CuWvilt2JKRVbtgMffk5hRhMpndKY73nFcP2P7OUuvEKk0bPrxN4BKqcmNB7EAOFtmSLtfEyzZzRt6OCAbAwggGsMB8GA1UdIwQYMBaAFJR5Q22ZaA/FWsypkn58HUCZKRdaMFcGA1UdIARQME4wTAYEVR0gADBEMEIGCCsGAQUFBwIBFjZodHRwczovL3d3dy50anpoY2EuY29tL0hvbWUvU3VwcG9ydFNlcnZpY2VzP3N0YXRlPXRhYjcwgZgGCCsGAQUFBwEBBIGLMIGIMEIGCCsGAQUFBzABhjZodHRwczovL3d3dy50anpoY2EuY29tL0hvbWUvU3VwcG9ydFNlcnZpY2VzP3N0YXRlPXRhYjMwQgYIKwYBBQUHMAKGNmh0dHBzOi8vd3d3LnRqemhjYS5jb20vSG9tZS9TdXBwb3J0U2VydmljZXM/c3RhdGU9dGFiMzA3BgNVHR8EMDAuMCygKqAohiZodHRwOi8veGtleS50anpoY2EuY29tOjEwMDIxLzkvMTc2LmNybDAJBgNVHRMEAjAAMCUGCCqBHNAUBAEBAQH/BBagFBMSNTQyMTI3MTk4MjA2MDcxODY3MB0GA1UdDgQWBBRMFWbm7HVnKp1oNEQ9ZJ/YZCk73jALBgNVHQ8EBAMCBsAwDAYIKoEcz1UBg3UFAANHADBEAiA28viGyig8s2LqkeggNBPdQyPTcd2N/8FoVJd2ThllbwIgb8zouCZHRAEA2TVKLZ1SlMbgj9YwsGHq92oVrxvJDao=&#39;, &#39;encCert&#39;: None, &#39;privateKey&#39;: &#39;AMhHLPnc6941Y6pEIhgmLX0jGSF3C8U94pRczqlR9sC+\n&#39;, &#39;extention&#39;: &#39;{&#34;encKey&#34;:&#34;&#34;,&#34;timeUsed&#34;:7,&#34;transactionCode&#34;:&#34;2506251526575849332&#34;}&#39;, &#39;certInfoId&#39;: -189818, &#39;algorithm&#39;: &#39;SM2&#39;}

</pre></div>
            </div>
          </div>
        

      </td>
    </tr>
  
    
    
    <tr id="record_1_3">
      <th class="error" style="width:5em;">error</th>
      <td colspan="2">步骤3-查询证书信息</td>
      <td style="text-align:center;width:6em;">N/A ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_3_1">log-1</a>
        <div id="popup_log_1_3_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_3_1">&times;</a>

            <div class="content">
              <h3>Name: </h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          N/A
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          N/A
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            N/A
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            None
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>N/A</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>N/A</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>N/A</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        
          <a class="button" href="#popup_attachment_1_3">traceback</a>
          <div id="popup_attachment_1_3" class="overlay">
            <div class="popup">
              <h2>Traceback Message</h2>
              <a class="close" href="#record_1_3">&times;</a>
              <div class="content"><pre>httprunner.exceptions.VariableNotFound: cert_info_id
</pre></div>
            </div>
          </div>
        

      </td>
    </tr>
  
    
    
    <tr id="record_1_4">
      <th class="error" style="width:5em;">error</th>
      <td colspan="2">步骤4-吊销证书</td>
      <td style="text-align:center;width:6em;">N/A ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_4_1">log-1</a>
        <div id="popup_log_1_4_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_4_1">&times;</a>

            <div class="content">
              <h3>Name: </h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          N/A
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          N/A
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            N/A
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            None
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>N/A</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>N/A</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>N/A</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        
          <a class="button" href="#popup_attachment_1_4">traceback</a>
          <div id="popup_attachment_1_4" class="overlay">
            <div class="popup">
              <h2>Traceback Message</h2>
              <a class="close" href="#record_1_4">&times;</a>
              <div class="content"><pre>httprunner.exceptions.VariableNotFound: cert_info_id
</pre></div>
            </div>
          </div>
        

      </td>
    </tr>
  
    
    
    <tr id="record_1_5">
      <th class="error" style="width:5em;">error</th>
      <td colspan="2">步骤5-验证证书已吊销</td>
      <td style="text-align:center;width:6em;">N/A ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_5_1">log-1</a>
        <div id="popup_log_1_5_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_5_1">&times;</a>

            <div class="content">
              <h3>Name: </h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          N/A
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          N/A
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            N/A
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            None
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>N/A</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>N/A</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>N/A</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        
          <a class="button" href="#popup_attachment_1_5">traceback</a>
          <div id="popup_attachment_1_5" class="overlay">
            <div class="popup">
              <h2>Traceback Message</h2>
              <a class="close" href="#record_1_5">&times;</a>
              <div class="content"><pre>httprunner.exceptions.VariableNotFound: cert_info_id
</pre></div>
            </div>
          </div>
        

      </td>
    </tr>
  
  </table>
  
</body>