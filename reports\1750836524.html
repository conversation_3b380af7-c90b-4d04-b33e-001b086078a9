<head>
  <meta content="text/html; charset=utf-8" http-equiv="content-type" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title> - TestReport</title>
  <style>
    body {
      background-color: #f2f2f2;
      color: #333;
      margin: 0 auto;
      width: 960px;
    }
    #summary {
      width: 960px;
      margin-bottom: 20px;
    }
    #summary th {
      background-color: skyblue;
      padding: 5px 12px;
    }
    #summary td {
      background-color: lightblue;
      text-align: center;
      padding: 4px 8px;
    }
    .details {
      width: 960px;
      margin-bottom: 20px;
    }
    .details th {
      background-color: skyblue;
      padding: 5px 12px;
    }
    .details tr .passed {
      background-color: lightgreen;
    }
    .details tr .failed {
      background-color: red;
    }
    .details tr .unchecked {
      background-color: gray;
    }
    .details td {
      background-color: lightblue;
      padding: 5px 12px;
    }
    .details .detail {
      background-color: lightgrey;
      font-size: smaller;
      padding: 5px 10px;
      line-height: 20px;
      text-align: left;
    }
    .details .success {
      background-color: greenyellow;
    }
    .details .error {
      background-color: red;
    }
    .details .failure {
      background-color: salmon;
    }
    .details .skipped {
      background-color: gray;
    }

    .button {
      font-size: 1em;
      padding: 6px;
      width: 4em;
      text-align: center;
      background-color: #06d85f;
      border-radius: 20px/50px;
      cursor: pointer;
      transition: all 0.3s ease-out;
    }
    a.button{
      color: gray;
      text-decoration: none;
      display: inline-block;
    }
    .button:hover {
      background: #2cffbd;
    }

    .overlay {
      position: fixed;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      background: rgba(0, 0, 0, 0.7);
      transition: opacity 500ms;
      visibility: hidden;
      opacity: 0;
      line-height: 25px;
    }
    .overlay:target {
      visibility: visible;
      opacity: 1;
    }

    .popup {
      margin: 70px auto;
      padding: 20px;
      background: #fff;
      border-radius: 10px;
      width: 50%;
      position: relative;
      transition: all 3s ease-in-out;
    }

    .popup h2 {
      margin-top: 0;
      color: #333;
      font-family: Tahoma, Arial, sans-serif;
    }
    .popup .close {
      position: absolute;
      top: 20px;
      right: 30px;
      transition: all 200ms;
      font-size: 30px;
      font-weight: bold;
      text-decoration: none;
      color: #333;
    }
    .popup .close:hover {
      color: #06d85f;
    }
    .popup .content {
      max-height: 80%;
      overflow: auto;
      text-align: left;
    }
    .popup .separator {
      color:royalblue
    }

    @media screen and (max-width: 700px) {
      .box {
        width: 70%;
      }
      .popup {
        width: 70%;
      }
    }

  </style>
</head>

<body>
  <h1>Test Report: </h1>

  <h2>Summary</h2>
  <table id="summary">
    <tr>
      <th>START AT</th>
      <td colspan="4">2025-06-25 15:28:44</td>
    </tr>
    <tr>
      <th>DURATION</th>
      <td colspan="4">0.071 seconds</td>
    </tr>
    <tr>
      <th>PLATFORM</th>
      <td>HttpRunner 2.2.4 </td>
      <td>CPython 3.9.7 </td>
      <td colspan="2">Windows-10-10.0.18363-SP0</td>
    </tr>
    <tr>
      <th>STAT</th>
      <th colspan="2">TESTCASES (success/fail)</th>
      <th colspan="2">TESTSTEPS (success/fail/error/skip)</th>
    </tr>
    <tr>
      <td>total (details) =></td>
      <td colspan="2">1 (0/1)</td>
      <td colspan="2">5 (0/1/4/0)</td>
    </tr>
  </table>

  <h2>Details</h2>

  
  
  <h3>证书全流程测试</h3>
  <table id="suite_1" class="details">
    <tr>
      <td>TOTAL: 5</td>
      <td>SUCCESS: 0</td>
      <td>FAILED: 1</td>
      <td>ERROR: 4</td>
      <td>SKIPPED: 0</td>
    </tr>
    <tr>
      <th>Status</th>
      <th colspan="2">Name</th>
      <th>Response Time</th>
      <th>Detail</th>
    </tr>

    
    
    
    <tr id="record_1_1">
      <th class="failure" style="width:5em;">failure</th>
      <td colspan="2">步骤1-获取测试账号</td>
      <td style="text-align:center;width:6em;">42.30 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_1_1">log-1</a>
        <div id="popup_log_1_1_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_1_1">&times;</a>

            <div class="content">
              <h3>Name: 步骤1-获取测试账号</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://sdk.testk8s.tsign.cn/random/get
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          GET
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://sdk.testk8s.tsign.cn/random/get
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            utf-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Connection</strong>: close
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 25 Jun 2025 07:28:44 GMT
                            </div>
                            
                            <div>
                              <strong>Vary</strong>: Accept-Encoding, Origin, Access-Control-Request-Method, Access-Control-Request-Headers
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;success&#39;: True, &#39;accountList&#39;: [{&#39;orgCode&#39;: &#39;91000000FDM044ED53&#39;, &#39;idNo&#39;: &#39;370281196101098933&#39;, &#39;name&#39;: &#39;测试江壮&#39;, &#39;englishName&#39;: &#39;Marcus Joe Martinez&#39;, &#39;bankCard&#39;: &#39;6210920053478475440&#39;, &#39;phone&#39;: &#39;***********&#39;, &#39;orgName&#39;: &#39;esigntest江壮经营的个体工商户&#39;}]}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>258</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>42.3</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>37.791</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        
          <a class="button" href="#popup_attachment_1_1">traceback</a>
          <div id="popup_attachment_1_1" class="overlay">
            <div class="popup">
              <h2>Traceback Message</h2>
              <a class="close" href="#record_1_1">&times;</a>
              <div class="content"><pre>Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 54, in test
    test_runner.run_test(test_dict)
httprunner.exceptions.ExtractFailure: Failed to extract! =&gt; accountList.0a.idcard
response body: {&#39;success&#39;: True, &#39;accountList&#39;: [{&#39;orgCode&#39;: &#39;91000000FDM044ED53&#39;, &#39;idNo&#39;: &#39;370281196101098933&#39;, &#39;name&#39;: &#39;测试江壮&#39;, &#39;englishName&#39;: &#39;Marcus Joe Martinez&#39;, &#39;bankCard&#39;: &#39;6210920053478475440&#39;, &#39;phone&#39;: &#39;***********&#39;, &#39;orgName&#39;: &#39;esigntest江壮经营的个体工商户&#39;}]}


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 56, in test
    self.fail(str(ex))
AssertionError: Failed to extract! =&gt; accountList.0a.idcard
response body: {&#39;success&#39;: True, &#39;accountList&#39;: [{&#39;orgCode&#39;: &#39;91000000FDM044ED53&#39;, &#39;idNo&#39;: &#39;370281196101098933&#39;, &#39;name&#39;: &#39;测试江壮&#39;, &#39;englishName&#39;: &#39;Marcus Joe Martinez&#39;, &#39;bankCard&#39;: &#39;6210920053478475440&#39;, &#39;phone&#39;: &#39;***********&#39;, &#39;orgName&#39;: &#39;esigntest江壮经营的个体工商户&#39;}]}

</pre></div>
            </div>
          </div>
        

      </td>
    </tr>
  
    
    
    <tr id="record_1_2">
      <th class="error" style="width:5em;">error</th>
      <td colspan="2">步骤2-创建证书</td>
      <td style="text-align:center;width:6em;">N/A ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_2_1">log-1</a>
        <div id="popup_log_1_2_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_2_1">&times;</a>

            <div class="content">
              <h3>Name: </h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          N/A
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          N/A
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            N/A
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            None
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>N/A</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>N/A</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>N/A</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        
          <a class="button" href="#popup_attachment_1_2">traceback</a>
          <div id="popup_attachment_1_2" class="overlay">
            <div class="popup">
              <h2>Traceback Message</h2>
              <a class="close" href="#record_1_2">&times;</a>
              <div class="content"><pre>httprunner.exceptions.VariableNotFound: phone
</pre></div>
            </div>
          </div>
        

      </td>
    </tr>
  
    
    
    <tr id="record_1_3">
      <th class="error" style="width:5em;">error</th>
      <td colspan="2">步骤3-查询证书信息</td>
      <td style="text-align:center;width:6em;">N/A ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_3_1">log-1</a>
        <div id="popup_log_1_3_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_3_1">&times;</a>

            <div class="content">
              <h3>Name: </h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          N/A
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          N/A
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            N/A
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            None
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>N/A</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>N/A</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>N/A</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        
          <a class="button" href="#popup_attachment_1_3">traceback</a>
          <div id="popup_attachment_1_3" class="overlay">
            <div class="popup">
              <h2>Traceback Message</h2>
              <a class="close" href="#record_1_3">&times;</a>
              <div class="content"><pre>httprunner.exceptions.VariableNotFound: cert_info_id
</pre></div>
            </div>
          </div>
        

      </td>
    </tr>
  
    
    
    <tr id="record_1_4">
      <th class="error" style="width:5em;">error</th>
      <td colspan="2">步骤4-吊销证书</td>
      <td style="text-align:center;width:6em;">N/A ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_4_1">log-1</a>
        <div id="popup_log_1_4_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_4_1">&times;</a>

            <div class="content">
              <h3>Name: </h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          N/A
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          N/A
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            N/A
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            None
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>N/A</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>N/A</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>N/A</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        
          <a class="button" href="#popup_attachment_1_4">traceback</a>
          <div id="popup_attachment_1_4" class="overlay">
            <div class="popup">
              <h2>Traceback Message</h2>
              <a class="close" href="#record_1_4">&times;</a>
              <div class="content"><pre>httprunner.exceptions.VariableNotFound: cert_info_id
</pre></div>
            </div>
          </div>
        

      </td>
    </tr>
  
    
    
    <tr id="record_1_5">
      <th class="error" style="width:5em;">error</th>
      <td colspan="2">步骤5-验证证书已吊销</td>
      <td style="text-align:center;width:6em;">N/A ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_5_1">log-1</a>
        <div id="popup_log_1_5_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_5_1">&times;</a>

            <div class="content">
              <h3>Name: </h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          N/A
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          N/A
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            N/A
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            None
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>N/A</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>N/A</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>N/A</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        
          <a class="button" href="#popup_attachment_1_5">traceback</a>
          <div id="popup_attachment_1_5" class="overlay">
            <div class="popup">
              <h2>Traceback Message</h2>
              <a class="close" href="#record_1_5">&times;</a>
              <div class="content"><pre>httprunner.exceptions.VariableNotFound: cert_info_id
</pre></div>
            </div>
          </div>
        

      </td>
    </tr>
  
  </table>
  
</body>