<head>
  <meta content="text/html; charset=utf-8" http-equiv="content-type" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title> - TestReport</title>
  <style>
    body {
      background-color: #f2f2f2;
      color: #333;
      margin: 0 auto;
      width: 960px;
    }
    #summary {
      width: 960px;
      margin-bottom: 20px;
    }
    #summary th {
      background-color: skyblue;
      padding: 5px 12px;
    }
    #summary td {
      background-color: lightblue;
      text-align: center;
      padding: 4px 8px;
    }
    .details {
      width: 960px;
      margin-bottom: 20px;
    }
    .details th {
      background-color: skyblue;
      padding: 5px 12px;
    }
    .details tr .passed {
      background-color: lightgreen;
    }
    .details tr .failed {
      background-color: red;
    }
    .details tr .unchecked {
      background-color: gray;
    }
    .details td {
      background-color: lightblue;
      padding: 5px 12px;
    }
    .details .detail {
      background-color: lightgrey;
      font-size: smaller;
      padding: 5px 10px;
      line-height: 20px;
      text-align: left;
    }
    .details .success {
      background-color: greenyellow;
    }
    .details .error {
      background-color: red;
    }
    .details .failure {
      background-color: salmon;
    }
    .details .skipped {
      background-color: gray;
    }

    .button {
      font-size: 1em;
      padding: 6px;
      width: 4em;
      text-align: center;
      background-color: #06d85f;
      border-radius: 20px/50px;
      cursor: pointer;
      transition: all 0.3s ease-out;
    }
    a.button{
      color: gray;
      text-decoration: none;
      display: inline-block;
    }
    .button:hover {
      background: #2cffbd;
    }

    .overlay {
      position: fixed;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      background: rgba(0, 0, 0, 0.7);
      transition: opacity 500ms;
      visibility: hidden;
      opacity: 0;
      line-height: 25px;
    }
    .overlay:target {
      visibility: visible;
      opacity: 1;
    }

    .popup {
      margin: 70px auto;
      padding: 20px;
      background: #fff;
      border-radius: 10px;
      width: 50%;
      position: relative;
      transition: all 3s ease-in-out;
    }

    .popup h2 {
      margin-top: 0;
      color: #333;
      font-family: Tahoma, Arial, sans-serif;
    }
    .popup .close {
      position: absolute;
      top: 20px;
      right: 30px;
      transition: all 200ms;
      font-size: 30px;
      font-weight: bold;
      text-decoration: none;
      color: #333;
    }
    .popup .close:hover {
      color: #06d85f;
    }
    .popup .content {
      max-height: 80%;
      overflow: auto;
      text-align: left;
    }
    .popup .separator {
      color:royalblue
    }

    @media screen and (max-width: 700px) {
      .box {
        width: 70%;
      }
      .popup {
        width: 70%;
      }
    }

  </style>
</head>

<body>
  <h1>Test Report: </h1>

  <h2>Summary</h2>
  <table id="summary">
    <tr>
      <th>START AT</th>
      <td colspan="4">2025-06-25 16:10:46</td>
    </tr>
    <tr>
      <th>DURATION</th>
      <td colspan="4">2.027 seconds</td>
    </tr>
    <tr>
      <th>PLATFORM</th>
      <td>HttpRunner 2.2.4 </td>
      <td>CPython 3.9.7 </td>
      <td colspan="2">Windows-10-10.0.18363-SP0</td>
    </tr>
    <tr>
      <th>STAT</th>
      <th colspan="2">TESTCASES (success/fail)</th>
      <th colspan="2">TESTSTEPS (success/fail/error/skip)</th>
    </tr>
    <tr>
      <td>total (details) =></td>
      <td colspan="2">2 (1/1)</td>
      <td colspan="2">17 (9/8/0/0)</td>
    </tr>
  </table>

  <h2>Details</h2>

  
  
  <h3>证书全流程测试</h3>
  <table id="suite_1" class="details">
    <tr>
      <td>TOTAL: 5</td>
      <td>SUCCESS: 5</td>
      <td>FAILED: 0</td>
      <td>ERROR: 0</td>
      <td>SKIPPED: 0</td>
    </tr>
    <tr>
      <th>Status</th>
      <th colspan="2">Name</th>
      <th>Response Time</th>
      <th>Detail</th>
    </tr>

    
    
    
    <tr id="record_1_1">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">步骤1-获取测试账号</td>
      <td style="text-align:center;width:6em;">115.97 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_1_1">log-1</a>
        <div id="popup_log_1_1_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_1_1">&times;</a>

            <div class="content">
              <h3>Name: 步骤1-获取测试账号</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://sdk.testk8s.tsign.cn/random/get
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          GET
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://sdk.testk8s.tsign.cn/random/get
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            utf-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Date</strong>: Wed, 25 Jun 2025 08:10:46 GMT
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Connection</strong>: keep-alive
                            </div>
                            
                            <div>
                              <strong>Vary</strong>: Accept-Encoding, Origin, Access-Control-Request-Method, Access-Control-Request-Headers
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;success&#39;: True, &#39;accountList&#39;: [{&#39;orgCode&#39;: &#39;910000001QWCEMCE9F&#39;, &#39;idNo&#39;: &#39;331100197904136897&#39;, &#39;name&#39;: &#39;测试言永&#39;, &#39;englishName&#39;: &#39;Jeff Dan Brooks&#39;, &#39;bankCard&#39;: &#39;****************&#39;, &#39;phone&#39;: &#39;***********&#39;, &#39;orgName&#39;: &#39;esigntest言永经营的个体工商户&#39;}]}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        status_code
                      </td>
                      <td>equals</td>
                      <td>200</td>
                      <td>200</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>251</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>115.97</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>112.368</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_2">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">步骤2-创建证书</td>
      <td style="text-align:center;width:6em;">284.46 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_2_1">log-1</a>
        <div id="popup_log_1_2_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_2_1">&times;</a>

            <div class="content">
              <h3>Name: 步骤2-创建证书</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://cert-service.testk8s.tsign.cn/openca/rest/cert/createnew
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: **********
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 389
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;certParam&#34;: {&#34;algorithm&#34;: &#34;SM2&#34;, &#34;certPolicy&#34;: &#34;COMMON&#34;, &#34;certTime&#34;: &#34;ONEYEAR&#34;, &#34;certType&#34;: &#34;SINGLE&#34;, &#34;configId&#34;: &#34;**********&#34;, &#34;isUkey&#34;: false, &#34;issuer&#34;: &#34;ZHCA&#34;}, &#34;commonParam&#34;: {&#34;address&#34;: &#34;\u5929\u5802\u8f6f\u4ef6\u56ed&#34;, &#34;mobile&#34;: &#34;***********&#34;, &#34;phone&#34;: &#34;***********&#34;}, &#34;userParam&#34;: {&#34;certName&#34;: &#34;\u6d4b\u8bd5\u8a00\u6c38&#34;, &#34;licenseNumber&#34;: &#34;331100197904136897&#34;, &#34;licenseType&#34;: 19}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://cert-service.testk8s.tsign.cn/openca/rest/cert/createnew
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Date</strong>: Wed, 25 Jun 2025 08:10:46 GMT
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Connection</strong>: keep-alive
                            </div>
                            
                            <div>
                              <strong>Vary</strong>: Accept-Encoding
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;errCode&#39;: 0, &#39;msg&#39;: &#39;成功&#39;, &#39;errShow&#39;: False, &#39;signCert&#39;: &#39;MIIC/DCCAqCgAwIBAgIMOJKZnauiIjHD4ecIMAwGCCqBHM9VAYN1BQAwKDEZMBcGA1UEAwwQVEVTVCBaSENBIFNNMiBDQTELMAkGA1UEBhMCQ04wHhcNMjUwNjI1MDgwODM1WhcNMjYwNjI1MDgwODM1WjAkMRUwEwYDVQQDDAzmtYvor5XoqIDmsLgxCzAJBgNVBAYTAkNOMFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEUHOc5HHWPXxV1ZMv4WlDVY3VKUOGOuBPIN/wQtP1c8ZXORpLN7EfunlM8pamEIpDBv+ny3RdgLZM0B1Bq4nRQ6OCAbAwggGsMB8GA1UdIwQYMBaAFJR5Q22ZaA/FWsypkn58HUCZKRdaMFcGA1UdIARQME4wTAYEVR0gADBEMEIGCCsGAQUFBwIBFjZodHRwczovL3d3dy50anpoY2EuY29tL0hvbWUvU3VwcG9ydFNlcnZpY2VzP3N0YXRlPXRhYjcwgZgGCCsGAQUFBwEBBIGLMIGIMEIGCCsGAQUFBzABhjZodHRwczovL3d3dy50anpoY2EuY29tL0hvbWUvU3VwcG9ydFNlcnZpY2VzP3N0YXRlPXRhYjMwQgYIKwYBBQUHMAKGNmh0dHBzOi8vd3d3LnRqemhjYS5jb20vSG9tZS9TdXBwb3J0U2VydmljZXM/c3RhdGU9dGFiMzA3BgNVHR8EMDAuMCygKqAohiZodHRwOi8veGtleS50anpoY2EuY29tOjEwMDIxLzkvMTc2LmNybDAJBgNVHRMEAjAAMCUGCCqBHNAUBAEBAQH/BBagFBMSMzMxMTAwMTk3OTA0MTM2ODk3MB0GA1UdDgQWBBS5bxEFHqrUIGN9cs+Oc2VoPMX2sjALBgNVHQ8EBAMCBsAwDAYIKoEcz1UBg3UFAANIADBFAiEA4AypM8vSm3T3ma1glMVpATtbXxzCl+jRpHcgq9KiHzACICKrePP9Pbvgv5LErrvDpQ9veItYAAFGaI/nQfyfJ6rz&#39;, &#39;encCert&#39;: None, &#39;privateKey&#39;: &#39;AJsmO3ai374lAGKDEZQtl2ATAu4iVEgQ4O2K3sDX+SoZ\n&#39;, &#39;extention&#39;: &#39;{&#34;encKey&#34;:&#34;&#34;,&#34;timeUsed&#34;:17,&#34;transactionCode&#34;:&#34;2506251608350169042&#34;}&#39;, &#39;certInfoId&#39;: -190402, &#39;algorithm&#39;: &#39;SM2&#39;}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        status_code
                      </td>
                      <td>equals</td>
                      <td>200</td>
                      <td>200</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.msg
                      </td>
                      <td>equals</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>1290</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>284.46</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>280.884</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_3">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">步骤3-查询证书信息</td>
      <td style="text-align:center;width:6em;">34.90 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_3_1">log-1</a>
        <div id="popup_log_1_3_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_3_1">&times;</a>

            <div class="content">
              <h3>Name: 步骤3-查询证书信息</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://cert-service.testk8s.tsign.cn/openca/rest/cert/detail
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: **********
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 49
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;certInfoId&#34;: -190402, &#34;configId&#34;: &#34;**********&#34;}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://cert-service.testk8s.tsign.cn/openca/rest/cert/detail
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Date</strong>: Wed, 25 Jun 2025 08:10:46 GMT
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Connection</strong>: keep-alive
                            </div>
                            
                            <div>
                              <strong>Vary</strong>: Accept-Encoding
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;errCode&#39;: 0, &#39;msg&#39;: &#39;成功&#39;, &#39;errShow&#39;: False, &#39;certInfo&#39;: {&#39;issuer&#39;: &#39;ZHCA&#39;, &#39;authcode&#39;: None, &#39;certname&#39;: &#39;测试言永&#39;, &#39;serialno&#39;: &#39;3892999daba22231c3e1e708&#39;, &#39;projectid&#39;: None, &#39;licensenumber&#39;: &#39;331100197904136897&#39;, &#39;id&#39;: -190402, &#39;isukey&#39;: 0, &#39;status&#39;: 1, &#39;certtime&#39;: 1, &#39;certtype&#39;: 1, &#39;algorithm&#39;: 2, &#39;certpolicy&#39;: 0, &#39;licensetype&#39;: 19, &#39;createTime&#39;: &#39;Jun 25, 2025 4:10:47 PM&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        status_code
                      </td>
                      <td>equals</td>
                      <td>200</td>
                      <td>200</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.certInfo.certname
                      </td>
                      <td>equals</td>
                      <td>LazyString($name)</td>
                      <td>测试言永</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>353</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>34.9</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>31.914</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_4">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">步骤4-吊销证书</td>
      <td style="text-align:center;width:6em;">92.32 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_4_1">log-1</a>
        <div id="popup_log_1_4_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_4_1">&times;</a>

            <div class="content">
              <h3>Name: 步骤4-吊销证书</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://cert-service.testk8s.tsign.cn/openca/rest/cert/revoke
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: **********
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 23
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;certInfoId&#34;: -190402}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://cert-service.testk8s.tsign.cn/openca/rest/cert/revoke
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Date</strong>: Wed, 25 Jun 2025 08:10:46 GMT
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Connection</strong>: keep-alive
                            </div>
                            
                            <div>
                              <strong>Vary</strong>: Accept-Encoding
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;errCode&#39;: 0, &#39;msg&#39;: &#39;成功&#39;, &#39;errShow&#39;: False}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        status_code
                      </td>
                      <td>equals</td>
                      <td>200</td>
                      <td>200</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.msg
                      </td>
                      <td>equals</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>44</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>92.32</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>90.801</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_1_5">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">步骤5-验证证书已吊销</td>
      <td style="text-align:center;width:6em;">50.86 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_1_5_1">log-1</a>
        <div id="popup_log_1_5_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_1_5_1">&times;</a>

            <div class="content">
              <h3>Name: 步骤5-验证证书已吊销</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://cert-service.testk8s.tsign.cn/openca/rest/cert/detail
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: **********
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 49
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;certInfoId&#34;: -190402, &#34;configId&#34;: &#34;**********&#34;}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://cert-service.testk8s.tsign.cn/openca/rest/cert/detail
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Date</strong>: Wed, 25 Jun 2025 08:10:47 GMT
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Content-Length</strong>: 63
                            </div>
                            
                            <div>
                              <strong>Connection</strong>: keep-alive
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;errCode&#39;: 850007, &#39;msg&#39;: &#39;证书记录不存在&#39;, &#39;errShow&#39;: True}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        status_code
                      </td>
                      <td>equals</td>
                      <td>200</td>
                      <td>200</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.msg
                      </td>
                      <td>equals</td>
                      <td>证书记录不存在</td>
                      <td>证书记录不存在</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>63</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>50.86</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>49.019</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
  </table>
  
  
  <h3>创建证书任务测试用例</h3>
  <table id="suite_2" class="details">
    <tr>
      <td>TOTAL: 12</td>
      <td>SUCCESS: 4</td>
      <td>FAILED: 8</td>
      <td>ERROR: 0</td>
      <td>SKIPPED: 0</td>
    </tr>
    <tr>
      <th>Status</th>
      <th colspan="2">Name</th>
      <th>Response Time</th>
      <th>Detail</th>
    </tr>

    
    
    
    <tr id="record_2_1">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">正向用例_创建证书任务</td>
      <td style="text-align:center;width:6em;">110.71 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_2_1_1">log-1</a>
        <div id="popup_log_2_1_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_2_1_1">&times;</a>

            <div class="content">
              <h3>Name: 正向用例_创建证书任务</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722915
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 484
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: &#34;**********&#34;, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19845524061, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1, &#34;certType&#34;: &#34;SINGLE&#34;, &#34;issuer&#34;: &#34;GUOXINCA&#34;, &#34;algorithm&#34;: &#34;RSA&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;\u6d4b\u8bd5\u515a\u548f\u5f69&#34;, &#34;licenseNumber&#34;: 370200196004223845, &#34;licenseType&#34;: 19, &#34;userType&#34;: 1}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 25 Jun 2025 08:10:47 GMT
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Connection</strong>: keep-alive
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 66
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>x-ts-request-id</strong>: 10100016160T282T17508390470552309
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T282T17508390470552309
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/3x8Mfqf&#39;, &#39;taskId&#39;: &#39;3e0bb1f0e6974d30810b265ffbce109c&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=3e0bb1f0e6974d30810b265ffbce109c&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        status_code
                      </td>
                      <td>equals</td>
                      <td>200</td>
                      <td>200</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>equals</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>110.71</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>109.205</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_2_2">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">枚举值_licenseType_19</td>
      <td style="text-align:center;width:6em;">55.89 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_2_2_1">log-1</a>
        <div id="popup_log_2_2_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_2_2_1">&times;</a>

            <div class="content">
              <h3>Name: 枚举值_licenseType_19</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722915
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 484
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: &#34;**********&#34;, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19845524061, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1, &#34;certType&#34;: &#34;SINGLE&#34;, &#34;issuer&#34;: &#34;GUOXINCA&#34;, &#34;algorithm&#34;: &#34;RSA&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;\u6d4b\u8bd5\u515a\u548f\u5f69&#34;, &#34;licenseNumber&#34;: 370200196004223845, &#34;licenseType&#34;: 19, &#34;userType&#34;: 1}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 25 Jun 2025 08:10:47 GMT
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Connection</strong>: keep-alive
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 30
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>x-ts-request-id</strong>: 10100016160T282T17508390471502311
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T282T17508390471502311
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/oLtXrf4&#39;, &#39;taskId&#39;: &#39;ebab09c0839f47a0b6b8340cf7c499ce&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=ebab09c0839f47a0b6b8340cf7c499ce&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        status_code
                      </td>
                      <td>equals</td>
                      <td>200</td>
                      <td>200</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>equals</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>55.89</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>54.165</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_2_3">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">枚举值_userType_1</td>
      <td style="text-align:center;width:6em;">52.86 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_2_3_1">log-1</a>
        <div id="popup_log_2_3_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_2_3_1">&times;</a>

            <div class="content">
              <h3>Name: 枚举值_userType_1</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722915
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 484
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: &#34;**********&#34;, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19845524061, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1, &#34;certType&#34;: &#34;SINGLE&#34;, &#34;issuer&#34;: &#34;GUOXINCA&#34;, &#34;algorithm&#34;: &#34;RSA&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;\u6d4b\u8bd5\u515a\u548f\u5f69&#34;, &#34;licenseNumber&#34;: 370200196004223845, &#34;licenseType&#34;: 19, &#34;userType&#34;: 1}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 25 Jun 2025 08:10:47 GMT
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Connection</strong>: keep-alive
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 27
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>x-ts-request-id</strong>: 10100016160T282T17508390472062315
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T282T17508390472062315
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/NpMfdTX&#39;, &#39;taskId&#39;: &#39;795b54982c484fc99c4252abd629ffa6&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=795b54982c484fc99c4252abd629ffa6&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        status_code
                      </td>
                      <td>equals</td>
                      <td>200</td>
                      <td>200</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>equals</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>52.86</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>50.355</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_2_4">
      <th class="failure" style="width:5em;">failure</th>
      <td colspan="2">非法枚举值_licenseType</td>
      <td style="text-align:center;width:6em;">49.36 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_2_4_1">log-1</a>
        <div id="popup_log_2_4_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_2_4_1">&times;</a>

            <div class="content">
              <h3>Name: 非法枚举值_licenseType</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722915
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 485
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: &#34;**********&#34;, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19845524061, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1, &#34;certType&#34;: &#34;SINGLE&#34;, &#34;issuer&#34;: &#34;GUOXINCA&#34;, &#34;algorithm&#34;: &#34;RSA&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;\u6d4b\u8bd5\u515a\u548f\u5f69&#34;, &#34;licenseNumber&#34;: 370200196004223845, &#34;licenseType&#34;: 999, &#34;userType&#34;: 1}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 25 Jun 2025 08:10:47 GMT
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Connection</strong>: keep-alive
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 28
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>x-ts-request-id</strong>: 10100016160T282T17508390472632317
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T282T17508390472632317
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 1451001, &#39;message&#39;: None, &#39;data&#39;: None}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        status_code
                      </td>
                      <td>equals</td>
                      <td>200</td>
                      <td>200</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="failed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>400</td>
                      <td>1451001</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="failed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>参数错误: licenseType不合法</td>
                      <td>None</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>43</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>49.36</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>46.683</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        
          <a class="button" href="#popup_attachment_2_4">traceback</a>
          <div id="popup_attachment_2_4" class="overlay">
            <div class="popup">
              <h2>Traceback Message</h2>
              <a class="close" href="#record_2_4">&times;</a>
              <div class="content"><pre>Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 54, in test
    test_runner.run_test(test_dict)
httprunner.exceptions.ValidationFailure: 
validate: content.code equals 400(int)	==&gt; fail
1451001(int) equals 400(int)

validate: content.message contains 参数错误: licenseType不合法(str)	==&gt; fail
None(NoneType) contains 参数错误: licenseType不合法(str)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 56, in test
    self.fail(str(ex))
AssertionError: 
validate: content.code equals 400(int)	==&gt; fail
1451001(int) equals 400(int)

validate: content.message contains 参数错误: licenseType不合法(str)	==&gt; fail
None(NoneType) contains 参数错误: licenseType不合法(str)
</pre></div>
            </div>
          </div>
        

      </td>
    </tr>
  
    
    
    <tr id="record_2_5">
      <th class="failure" style="width:5em;">failure</th>
      <td colspan="2">缺失必填参数_bizType</td>
      <td style="text-align:center;width:6em;">39.89 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_2_5_1">log-1</a>
        <div id="popup_log_2_5_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_2_5_1">&times;</a>

            <div class="content">
              <h3>Name: 缺失必填参数_bizType</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722915
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 463
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: &#34;**********&#34;, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19845524061, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1, &#34;certType&#34;: &#34;SINGLE&#34;, &#34;issuer&#34;: &#34;GUOXINCA&#34;, &#34;algorithm&#34;: &#34;RSA&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;\u6d4b\u8bd5\u515a\u548f\u5f69&#34;, &#34;licenseNumber&#34;: 370200196004223845, &#34;licenseType&#34;: 19, &#34;userType&#34;: 1}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 25 Jun 2025 08:10:47 GMT
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Connection</strong>: keep-alive
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 13
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>x-ts-request-id</strong>: 10100016160T282T17508390473192319
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T282T17508390473192319
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 1451000, &#39;message&#39;: &#39;参数错误: bizType不能为空&#39;, &#39;data&#39;: None}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        status_code
                      </td>
                      <td>equals</td>
                      <td>200</td>
                      <td>200</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="failed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>400</td>
                      <td>1451000</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>参数错误: bizType不能为空</td>
                      <td>参数错误: bizType不能为空</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>74</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>39.89</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>38.131</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        
          <a class="button" href="#popup_attachment_2_5">traceback</a>
          <div id="popup_attachment_2_5" class="overlay">
            <div class="popup">
              <h2>Traceback Message</h2>
              <a class="close" href="#record_2_5">&times;</a>
              <div class="content"><pre>Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 54, in test
    test_runner.run_test(test_dict)
httprunner.exceptions.ValidationFailure: 
validate: content.code equals 400(int)	==&gt; fail
1451000(int) equals 400(int)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 56, in test
    self.fail(str(ex))
AssertionError: 
validate: content.code equals 400(int)	==&gt; fail
1451000(int) equals 400(int)
</pre></div>
            </div>
          </div>
        

      </td>
    </tr>
  
    
    
    <tr id="record_2_6">
      <th class="failure" style="width:5em;">failure</th>
      <td colspan="2">缺失必填参数_operatorType</td>
      <td style="text-align:center;width:6em;">30.92 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_2_6_1">log-1</a>
        <div id="popup_log_2_6_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_2_6_1">&times;</a>

            <div class="content">
              <h3>Name: 缺失必填参数_operatorType</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722915
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 459
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;projectId&#34;: &#34;**********&#34;, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19845524061, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1, &#34;certType&#34;: &#34;SINGLE&#34;, &#34;issuer&#34;: &#34;GUOXINCA&#34;, &#34;algorithm&#34;: &#34;RSA&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;\u6d4b\u8bd5\u515a\u548f\u5f69&#34;, &#34;licenseNumber&#34;: 370200196004223845, &#34;licenseType&#34;: 19, &#34;userType&#34;: 1}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 25 Jun 2025 08:10:47 GMT
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Connection</strong>: keep-alive
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 11
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>x-ts-request-id</strong>: 10100016160T282T17508390473582321
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T282T17508390473582321
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 1451000, &#39;message&#39;: &#39;参数错误: operatorType不能为空&#39;, &#39;data&#39;: None}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        status_code
                      </td>
                      <td>equals</td>
                      <td>200</td>
                      <td>200</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="failed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>400</td>
                      <td>1451000</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>参数错误: operatorType不能为空</td>
                      <td>参数错误: operatorType不能为空</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>79</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>30.92</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>29.27</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        
          <a class="button" href="#popup_attachment_2_6">traceback</a>
          <div id="popup_attachment_2_6" class="overlay">
            <div class="popup">
              <h2>Traceback Message</h2>
              <a class="close" href="#record_2_6">&times;</a>
              <div class="content"><pre>Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 54, in test
    test_runner.run_test(test_dict)
httprunner.exceptions.ValidationFailure: 
validate: content.code equals 400(int)	==&gt; fail
1451000(int) equals 400(int)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 56, in test
    self.fail(str(ex))
AssertionError: 
validate: content.code equals 400(int)	==&gt; fail
1451000(int) equals 400(int)
</pre></div>
            </div>
          </div>
        

      </td>
    </tr>
  
    
    
    <tr id="record_2_7">
      <th class="failure" style="width:5em;">failure</th>
      <td colspan="2">缺失必填参数_projectId</td>
      <td style="text-align:center;width:6em;">47.87 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_2_7_1">log-1</a>
        <div id="popup_log_2_7_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_2_7_1">&times;</a>

            <div class="content">
              <h3>Name: 缺失必填参数_projectId</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722915
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 457
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19845524061, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1, &#34;certType&#34;: &#34;SINGLE&#34;, &#34;issuer&#34;: &#34;GUOXINCA&#34;, &#34;algorithm&#34;: &#34;RSA&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;\u6d4b\u8bd5\u515a\u548f\u5f69&#34;, &#34;licenseNumber&#34;: 370200196004223845, &#34;licenseType&#34;: 19, &#34;userType&#34;: 1}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 25 Jun 2025 08:10:47 GMT
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Connection</strong>: keep-alive
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 25
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>x-ts-request-id</strong>: 10100016160T282T17508390473942323
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T282T17508390473942323
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/MC7kn4K&#39;, &#39;taskId&#39;: &#39;3cabba3c7f504c1880d3cdaf64ba82b9&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=3cabba3c7f504c1880d3cdaf64ba82b9&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        status_code
                      </td>
                      <td>equals</td>
                      <td>200</td>
                      <td>200</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="failed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>400</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="failed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>参数错误: projectId不能为空</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>47.87</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>46.202</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        
          <a class="button" href="#popup_attachment_2_7">traceback</a>
          <div id="popup_attachment_2_7" class="overlay">
            <div class="popup">
              <h2>Traceback Message</h2>
              <a class="close" href="#record_2_7">&times;</a>
              <div class="content"><pre>Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 54, in test
    test_runner.run_test(test_dict)
httprunner.exceptions.ValidationFailure: 
validate: content.code equals 400(int)	==&gt; fail
0(int) equals 400(int)

validate: content.message contains 参数错误: projectId不能为空(str)	==&gt; fail
成功(str) contains 参数错误: projectId不能为空(str)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 56, in test
    self.fail(str(ex))
AssertionError: 
validate: content.code equals 400(int)	==&gt; fail
0(int) equals 400(int)

validate: content.message contains 参数错误: projectId不能为空(str)	==&gt; fail
成功(str) contains 参数错误: projectId不能为空(str)
</pre></div>
            </div>
          </div>
        

      </td>
    </tr>
  
    
    
    <tr id="record_2_8">
      <th class="failure" style="width:5em;">failure</th>
      <td colspan="2">缺失必填参数_notifyUrl</td>
      <td style="text-align:center;width:6em;">46.88 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_2_8_1">log-1</a>
        <div id="popup_log_2_8_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_2_8_1">&times;</a>

            <div class="content">
              <h3>Name: 缺失必填参数_notifyUrl</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722915
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 431
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: &#34;**********&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19845524061, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1, &#34;certType&#34;: &#34;SINGLE&#34;, &#34;issuer&#34;: &#34;GUOXINCA&#34;, &#34;algorithm&#34;: &#34;RSA&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;\u6d4b\u8bd5\u515a\u548f\u5f69&#34;, &#34;licenseNumber&#34;: 370200196004223845, &#34;licenseType&#34;: 19, &#34;userType&#34;: 1}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 25 Jun 2025 08:10:47 GMT
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Connection</strong>: keep-alive
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 25
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>x-ts-request-id</strong>: 10100016160T282T17508390474472327
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T282T17508390474472327
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/VuiCvRY&#39;, &#39;taskId&#39;: &#39;6415e67f43264903b652dfccb25cdb38&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=6415e67f43264903b652dfccb25cdb38&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        status_code
                      </td>
                      <td>equals</td>
                      <td>200</td>
                      <td>200</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="failed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>400</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="failed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>参数错误: notifyUrl不能为空</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>46.88</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>44.63</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        
          <a class="button" href="#popup_attachment_2_8">traceback</a>
          <div id="popup_attachment_2_8" class="overlay">
            <div class="popup">
              <h2>Traceback Message</h2>
              <a class="close" href="#record_2_8">&times;</a>
              <div class="content"><pre>Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 54, in test
    test_runner.run_test(test_dict)
httprunner.exceptions.ValidationFailure: 
validate: content.code equals 400(int)	==&gt; fail
0(int) equals 400(int)

validate: content.message contains 参数错误: notifyUrl不能为空(str)	==&gt; fail
成功(str) contains 参数错误: notifyUrl不能为空(str)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 56, in test
    self.fail(str(ex))
AssertionError: 
validate: content.code equals 400(int)	==&gt; fail
0(int) equals 400(int)

validate: content.message contains 参数错误: notifyUrl不能为空(str)	==&gt; fail
成功(str) contains 参数错误: notifyUrl不能为空(str)
</pre></div>
            </div>
          </div>
        

      </td>
    </tr>
  
    
    
    <tr id="record_2_9">
      <th class="failure" style="width:5em;">failure</th>
      <td colspan="2">缺失必填参数_phone</td>
      <td style="text-align:center;width:6em;">47.88 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_2_9_1">log-1</a>
        <div id="popup_log_2_9_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_2_9_1">&times;</a>

            <div class="content">
              <h3>Name: 缺失必填参数_phone</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722915
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 462
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: &#34;**********&#34;, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1, &#34;certType&#34;: &#34;SINGLE&#34;, &#34;issuer&#34;: &#34;GUOXINCA&#34;, &#34;algorithm&#34;: &#34;RSA&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;\u6d4b\u8bd5\u515a\u548f\u5f69&#34;, &#34;licenseNumber&#34;: 370200196004223845, &#34;licenseType&#34;: 19, &#34;userType&#34;: 1}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 25 Jun 2025 08:10:47 GMT
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Connection</strong>: keep-alive
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 25
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>x-ts-request-id</strong>: 10100016160T282T17508390474992329
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T282T17508390474992329
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/f7u9QLK&#39;, &#39;taskId&#39;: &#39;08a8d744a0744f3b9b4aba1ac92b1f78&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=08a8d744a0744f3b9b4aba1ac92b1f78&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        status_code
                      </td>
                      <td>equals</td>
                      <td>200</td>
                      <td>200</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="failed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>400</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="failed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>参数错误: phone不能为空</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>47.88</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>46.196</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        
          <a class="button" href="#popup_attachment_2_9">traceback</a>
          <div id="popup_attachment_2_9" class="overlay">
            <div class="popup">
              <h2>Traceback Message</h2>
              <a class="close" href="#record_2_9">&times;</a>
              <div class="content"><pre>Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 54, in test
    test_runner.run_test(test_dict)
httprunner.exceptions.ValidationFailure: 
validate: content.code equals 400(int)	==&gt; fail
0(int) equals 400(int)

validate: content.message contains 参数错误: phone不能为空(str)	==&gt; fail
成功(str) contains 参数错误: phone不能为空(str)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 56, in test
    self.fail(str(ex))
AssertionError: 
validate: content.code equals 400(int)	==&gt; fail
0(int) equals 400(int)

validate: content.message contains 参数错误: phone不能为空(str)	==&gt; fail
成功(str) contains 参数错误: phone不能为空(str)
</pre></div>
            </div>
          </div>
        

      </td>
    </tr>
  
    
    
    <tr id="record_2_10">
      <th class="failure" style="width:5em;">failure</th>
      <td colspan="2">缺失嵌套必填参数_applyConfigModel.certNum</td>
      <td style="text-align:center;width:6em;">65.82 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_2_10_1">log-1</a>
        <div id="popup_log_2_10_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_2_10_1">&times;</a>

            <div class="content">
              <h3>Name: 缺失嵌套必填参数_applyConfigModel.certNum</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722915
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 470
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: &#34;**********&#34;, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19845524061, &#34;applyConfigModel&#34;: {&#34;certType&#34;: &#34;SINGLE&#34;, &#34;issuer&#34;: &#34;GUOXINCA&#34;, &#34;algorithm&#34;: &#34;RSA&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;\u6d4b\u8bd5\u515a\u548f\u5f69&#34;, &#34;licenseNumber&#34;: 370200196004223845, &#34;licenseType&#34;: 19, &#34;userType&#34;: 1}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 25 Jun 2025 08:10:47 GMT
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Connection</strong>: keep-alive
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 28
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>x-ts-request-id</strong>: 10100016160T282T17508390475662331
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T282T17508390475662331
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/PVz5zaW&#39;, &#39;taskId&#39;: &#39;e250376a16414e6c8150c939fd3d0265&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=e250376a16414e6c8150c939fd3d0265&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        status_code
                      </td>
                      <td>equals</td>
                      <td>200</td>
                      <td>200</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="failed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>400</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="failed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>参数错误: certNum不能为空</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>65.82</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>64.023</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        
          <a class="button" href="#popup_attachment_2_10">traceback</a>
          <div id="popup_attachment_2_10" class="overlay">
            <div class="popup">
              <h2>Traceback Message</h2>
              <a class="close" href="#record_2_10">&times;</a>
              <div class="content"><pre>Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 54, in test
    test_runner.run_test(test_dict)
httprunner.exceptions.ValidationFailure: 
validate: content.code equals 400(int)	==&gt; fail
0(int) equals 400(int)

validate: content.message contains 参数错误: certNum不能为空(str)	==&gt; fail
成功(str) contains 参数错误: certNum不能为空(str)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 56, in test
    self.fail(str(ex))
AssertionError: 
validate: content.code equals 400(int)	==&gt; fail
0(int) equals 400(int)

validate: content.message contains 参数错误: certNum不能为空(str)	==&gt; fail
成功(str) contains 参数错误: certNum不能为空(str)
</pre></div>
            </div>
          </div>
        

      </td>
    </tr>
  
    
    
    <tr id="record_2_11">
      <th class="success" style="width:5em;">success</th>
      <td colspan="2">枚举值_certType_SINGLE</td>
      <td style="text-align:center;width:6em;">49.87 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_2_11_1">log-1</a>
        <div id="popup_log_2_11_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_2_11_1">&times;</a>

            <div class="content">
              <h3>Name: 枚举值_certType_SINGLE</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722915
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 484
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: &#34;**********&#34;, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19845524061, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1, &#34;certType&#34;: &#34;SINGLE&#34;, &#34;issuer&#34;: &#34;GUOXINCA&#34;, &#34;algorithm&#34;: &#34;RSA&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;\u6d4b\u8bd5\u515a\u548f\u5f69&#34;, &#34;licenseNumber&#34;: 370200196004223845, &#34;licenseType&#34;: 19, &#34;userType&#34;: 1}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 25 Jun 2025 08:10:47 GMT
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Connection</strong>: keep-alive
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 24
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>x-ts-request-id</strong>: 10100016160T282T17508390476212333
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T282T17508390476212333
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/uiEtUpC&#39;, &#39;taskId&#39;: &#39;7dd3452bf07e425c946cdec2f13c93e9&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=7dd3452bf07e425c946cdec2f13c93e9&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        status_code
                      </td>
                      <td>equals</td>
                      <td>200</td>
                      <td>200</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>0</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        content.message
                      </td>
                      <td>equals</td>
                      <td>成功</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>49.87</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>48.528</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        

      </td>
    </tr>
  
    
    
    <tr id="record_2_12">
      <th class="failure" style="width:5em;">failure</th>
      <td colspan="2">非法枚举值_certType</td>
      <td style="text-align:center;width:6em;">49.88 ms</td>
      <td class="detail">

        
        
        <a class="button" href="#popup_log_2_12_1">log-1</a>
        <div id="popup_log_2_12_1" class="overlay">
          <div class="popup">
            <h2>Request and Response data</h2>
            <a class="close" href="#record_2_12_1">&times;</a>

            <div class="content">
              <h3>Name: 非法枚举值_certType</h3>

              

              

              <h3>Request:</h3>
              <div style="overflow: auto">
                <table>
                  
                    <tr>
                      <th>url</th>
                      <td>
                        
                          http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>method</th>
                      <td>
                        
                          POST
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>headers</th>
                      <td>
                        
                          
                          <div>
                            <strong>User-Agent</strong>: python-requests/2.26.0
                          </div>
                          
                          <div>
                            <strong>Accept-Encoding</strong>: gzip, deflate
                          </div>
                          
                          <div>
                            <strong>Accept</strong>: */*
                          </div>
                          
                          <div>
                            <strong>Connection</strong>: keep-alive
                          </div>
                          
                          <div>
                            <strong>Content-Type</strong>: application/json
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-App-Id</strong>: 7876722915
                          </div>
                          
                          <div>
                            <strong>X-Tsign-Open-Auth-Mode</strong>: simple
                          </div>
                          
                          <div>
                            <strong>Content-Length</strong>: 490
                          </div>
                          
                        
                      </td>
                    </tr>
                  
                    <tr>
                      <th>body</th>
                      <td>
                        
                          {&#34;bizType&#34;: &#34;TCLOUD&#34;, &#34;operatorType&#34;: &#34;APPLY&#34;, &#34;projectId&#34;: &#34;**********&#34;, &#34;notifyUrl&#34;: &#34;http://libaohui.com.cn/callback/ding&#34;, &#34;redirectUrl&#34;: &#34;https://www.baidu.com/?ie=utf-8&amp;tn=25017023_17_dg&amp;wd=bai&#34;, &#34;phone&#34;: 19845524061, &#34;applyConfigModel&#34;: {&#34;certNum&#34;: 1, &#34;certType&#34;: &#34;INVALID_TYPE&#34;, &#34;issuer&#34;: &#34;GUOXINCA&#34;, &#34;algorithm&#34;: &#34;RSA&#34;}, &#34;applyCommonModel&#34;: {}, &#34;applyUserModel&#34;: {&#34;certName&#34;: &#34;\u6d4b\u8bd5\u515a\u548f\u5f69&#34;, &#34;licenseNumber&#34;: 370200196004223845, &#34;licenseType&#34;: 19, &#34;userType&#34;: 1}}
                        
                      </td>
                    </tr>
                  
                </table>
              </div>

              <h3>Response:</h3>
              <div style="overflow: auto">
                <table>
                    
                      <tr>
                        <th>ok</th>
                        <td>
                          
                            True
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>url</th>
                        <td>
                          
                            http://in-test-openapi.tsign.cn/v1/certs/create-cert-task
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>status_code</th>
                        <td>
                          
                            200
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>reason</th>
                        <td>
                          
                            OK
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>cookies</th>
                        <td>
                          
                            {}
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>encoding</th>
                        <td>
                          
                            UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>headers</th>
                        <td>
                          
                            
                            <div>
                              <strong>Server</strong>: openresty
                            </div>
                            
                            <div>
                              <strong>Date</strong>: Wed, 25 Jun 2025 08:10:47 GMT
                            </div>
                            
                            <div>
                              <strong>Content-Type</strong>: application/json;charset=UTF-8
                            </div>
                            
                            <div>
                              <strong>Transfer-Encoding</strong>: chunked
                            </div>
                            
                            <div>
                              <strong>Connection</strong>: keep-alive
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Elapse-Time</strong>: 25
                            </div>
                            
                            <div>
                              <strong>Content-Encoding</strong>: gzip
                            </div>
                            
                            <div>
                              <strong>x-ts-request-id</strong>: 10100016160T282T17508390476752335
                            </div>
                            
                            <div>
                              <strong>X-Application-Context</strong>: application
                            </div>
                            
                            <div>
                              <strong>X-Tsign-Trace-Id</strong>: 10100016160T282T17508390476752335
                            </div>
                            
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>content_type</th>
                        <td>
                          
                            application/json;charset=UTF-8
                          
                        </td>
                      </tr>
                    
                      <tr>
                        <th>json</th>
                        <td>
                          
                              <pre>{&#39;code&#39;: 0, &#39;message&#39;: &#39;成功&#39;, &#39;data&#39;: {&#39;taskUrl&#39;: &#39;https://testt.tsign.cn/H1ucGHb&#39;, &#39;taskId&#39;: &#39;e039e12db818470a85fb2cae65eca6d2&#39;, &#39;taskLongUrl&#39;: &#39;https://testh5.tsign.cn/auth/caRA?taskId=e039e12db818470a85fb2cae65eca6d2&#39;}}</pre>
                          
                        </td>
                      </tr>
                    
                  </table>
              </div>
              

              <h3>Validators:</h3>
              <div style="overflow: auto">
                  <table>
                    <tr>
                      <th>check</th>
                      <th>comparator</th>
                      <th>expect value</th>
                      <th>actual value</th>
                    </tr>
                    
                    <tr>
                      
                      <td class="passed">
                      
                        status_code
                      </td>
                      <td>equals</td>
                      <td>200</td>
                      <td>200</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="failed">
                      
                        content.code
                      </td>
                      <td>equals</td>
                      <td>400</td>
                      <td>0</td>
                    </tr>
                    
                    <tr>
                      
                      <td class="failed">
                      
                        content.message
                      </td>
                      <td>contains</td>
                      <td>参数错误: certType不合法</td>
                      <td>成功</td>
                    </tr>
                    
                  </table>
              </div>

              <h3>Statistics:</h3>
              <div style="overflow: auto">
                <table>
                  <tr>
                      <th>content_size(bytes)</th>
                      <td>215</td>
                    </tr>
                  <tr>
                    <th>response_time(ms)</th>
                    <td>49.88</td>
                  </tr>
                  <tr>
                    <th>elapsed(ms)</th>
                    <td>48.606</td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
        </div>
        

        
          <a class="button" href="#popup_attachment_2_12">traceback</a>
          <div id="popup_attachment_2_12" class="overlay">
            <div class="popup">
              <h2>Traceback Message</h2>
              <a class="close" href="#record_2_12">&times;</a>
              <div class="content"><pre>Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 54, in test
    test_runner.run_test(test_dict)
httprunner.exceptions.ValidationFailure: 
validate: content.code equals 400(int)	==&gt; fail
0(int) equals 400(int)

validate: content.message contains 参数错误: certType不合法(str)	==&gt; fail
成功(str) contains 参数错误: certType不合法(str)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File &#34;d:\allprojects\pythonproject\footstone_集测专用项目\venv\lib\site-packages\httprunner\api.py&#34;, line 56, in test
    self.fail(str(ex))
AssertionError: 
validate: content.code equals 400(int)	==&gt; fail
0(int) equals 400(int)

validate: content.message contains 参数错误: certType不合法(str)	==&gt; fail
成功(str) contains 参数错误: certType不合法(str)
</pre></div>
            </div>
          </div>
        

      </td>
    </tr>
  
  </table>
  
</body>